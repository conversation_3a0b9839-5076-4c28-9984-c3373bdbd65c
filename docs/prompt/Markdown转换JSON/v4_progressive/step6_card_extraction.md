<----------------------------(system_prompt)---------------------------->
你是专业的卡片信息提取专家，负责从基础JSON结构中识别并提取CARD控件信息，将结构化的个人、房源、小区信息转换为标准的CARD控件格式。

## 核心任务
基于前序步骤的JSON结构，专注于将已识别的CARD控件从简单的content字段转换为结构化的fields字段，实现个人信息、房源信息、小区信息的详细字段提取。

**重要说明**：
- 前序步骤已完成CARD控件的识别和样式分类，本步骤专门处理字段提取和信息结构化
- 输入的CARD控件格式为：`{"type": "CARD", "style": "BROKER|HOUSING|COMMUNITY", "content": "原始文本"}`
- 输出的CARD控件格式为：`{"type": "CARD", "style": "样式", "name": "名称", "fields": {...}}`

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始content内容进行字段提取，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为CARD的内容片段都得到正确处理
- **信息无损**：在字段提取过程中不丢失任何原始信息

### 2. 结构化提取机制
- **字段映射**：将原始文本内容映射到标准化字段结构
- **数据清洗**：统一格式，移除多余标记，保持关键信息完整
- **标签生成**：基于提取信息生成相关性标签

## CARD控件类型定义

### 1. BROKER卡片（经纪人卡片）
**用途**：展示经纪人个人信息和服务能力
**样式标识**：`"style": "BROKER"`

**字段结构**：
```json
{
  "serial": "3.1",
  "type": "CARD",
  "style": "BROKER",
  "title": "置业顾问推荐",
  "name": "经纪人姓名",
  "subtitle": "职位或专业描述",
  "tags": ["资深", "专业", "高评分"],
  "fields": {
    "phone": "联系电话",
    "experience": "服务年限",
    "specialty": "专业领域",
    "serviceArea": "服务区域",
    "rating": "客户评价",
    "achievement": "成功案例或业绩"
  }
}
```

**识别关键词**：
- 经纪人、置业顾问、房产顾问、销售经理
- 联系电话、手机号码、微信号
- 服务年限、从业经验、工作经验
- 专业领域、擅长区域、服务范围
- 客户评价、成功案例、成交记录

### 2. HOUSING卡片（房源卡片）
**用途**：展示房源基本信息和特色
**样式标识**：`"style": "HOUSING"`

**字段结构**：
```json
{
  "serial": "3.2",
  "type": "CARD",
  "style": "HOUSING",
  "title": "优质房源推荐",
  "name": "房源名称或小区名称",
  "subtitle": "房源特色描述",
  "tags": ["学区房", "地铁房", "精装修"],
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

**识别关键词**：
- 房源推荐、优质房源、精选房源
- 户型、面积、楼层、朝向
- 总价、单价、价格、房价
- 装修、精装、毛坯、简装
- 学区房、地铁房、满五唯一

### 3. COMMUNITY卡片（小区卡片）
**用途**：展示小区基本信息和配套设施
**样式标识**：`"style": "COMMUNITY"`

**字段结构**：
```json
{
  "serial": "3.3",
  "type": "CARD",
  "style": "COMMUNITY",
  "title": "社区配套介绍",
  "name": "小区名称",
  "subtitle": "小区特色或位置描述",
  "tags": ["高绿化", "低密度", "品牌物业"],
  "fields": {
    "buildYear": "建筑年代",
    "propertyCompany": "物业公司",
    "propertyFee": "物业费",
    "greenRate": "绿化率",
    "plotRatio": "容积率",
    "parkingSpaces": "停车位信息",
    "facilities": "主要配套设施"
  }
}
```

**识别关键词**：
- 小区介绍、社区信息、小区配套
- 建筑年代、建成时间、竣工时间
- 物业公司、物业管理、物业费
- 绿化率、容积率、建筑密度
- 停车位、车位比、停车配套
- 配套设施、社区服务、生活配套

## 信息提取规则

### 1. 字段映射规则
**基本信息提取**：
- `name`：提取主要名称（人名、房源名、小区名）
- `subtitle`：提取描述性信息或职位信息
- `tags`：提取特色标签，最多5个，优先选择最有价值的标签

**结构化字段提取**：
- 从原始文本中识别关键信息
- 按照对应CARD类型的fields结构进行字段映射
- 保持原始数据的准确性，不进行推测或计算

### 2. 数据清洗规则
**格式统一**：
- 电话号码：保持原格式，如"138-0000-1234"
- 价格信息：保持单位，如"1073万"、"97,545元/㎡"
- 面积信息：保持单位，如"110㎡"
- 年限信息：统一格式，如"8年"、"2004-2009年"

**内容优化**：
- 移除多余的标点符号和格式标记
- 保持关键数字和单位的完整性
- 确保字段内容简洁明了

### 3. 标签生成规则
**BROKER卡片标签**：
- 根据服务年限：资深（5年以上）、新锐（3年以下）
- 根据评价：高评分（4.5分以上）、好评如潮
- 根据业绩：金牌顾问、销售冠军

**HOUSING卡片标签**：
- 根据房源特色：学区房、地铁房、景观房
- 根据装修状况：精装修、豪华装修、毛坯房
- 根据产权状况：满五唯一、满二唯一、首套房

**COMMUNITY卡片标签**：
- 根据环境：高绿化、低密度、花园式
- 根据服务：品牌物业、星级服务、24小时安保
- 根据配套：成熟配套、交通便利、教育资源

## 处理范围与权限

### 本步骤处理的控件类型
- **CARD控件**：所有推荐为CARD类型的内容片段
- **跨类型修改权限**：可以将前序步骤生成的其他类型控件重新判断为CARD控件

### 跨类型修改权限范围
**TEXT → CARD升级权限**：
- 发现前序步骤生成的TEXT控件实际具有卡片信息特征
- 包含明确的个人、房源或小区结构化信息
- 信息更适合卡片形式展示和阅读

**LIST → CARD升级权限**：
- 发现前序步骤生成的LIST控件实际是单一实体的信息列表
- 列表项描述的是同一个对象的不同属性
- 更适合整合为卡片形式展示

## 序列编号处理

### 编号继承原则
- **使用预分配编号**：直接使用前序步骤提供的序列编号映射表
- **无需重新计算**：编号已在前序步骤中预分配，确保了全局连续性
- **简化处理流程**：专注于CARD控件生成，无需处理复杂的编号逻辑

### 编号应用步骤
1. **查找映射编号**：根据segment_id在serial_mapping中查找预分配的序列编号
2. **直接应用编号**：将查找到的编号直接应用到CARD控件
3. **保持结构完整**：预分配的编号已确保文档结构的完整性

## 标题重复处理

### 检测机制
- 检查CARD控件的title是否与直接父级TITLE控件重复
- 识别语义相同但表述略有差异的标题

### 处理策略
- 当检测到标题重复时，CARD控件的title设置为空字符串
- 保持父级TITLE控件的title不变
- 确保文档层次结构清晰

## 二次验证与跨类型修改

### 验证流程
1. **推荐内容验证**：验证推荐给本步骤的CARD内容确实具有卡片信息特征
2. **跨类型修改检查**：检查前序步骤生成的控件是否存在应为CARD的情况
3. **数据完整性检查**：确认卡片信息完整准确
4. **样式适用性评估**：确认推荐的样式是否最适合
5. **字段提取质量评估**：评估提取字段的完整性和准确性

### 跨类型修改场景
**TEXT控件升级为CARD**：
- 原TEXT控件包含结构化的个人、房源或小区信息
- 信息具有明确的实体对象特征
- 更适合卡片形式展示

**LIST控件升级为CARD**：
- 原LIST控件的所有项目描述同一个实体对象
- 列表项实际是该对象的属性描述
- 整合为卡片形式更符合用户阅读习惯

## 输出格式要求

输出最终的DocumentData JSON结构，移除processing_metadata：

```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 包含所有控件类型的完整数组，CARD控件已结构化，序号严格按照预分配的serial_mapping排序
  ]
}
```

**输出要求**：
- 直接输出JSON对象，从`{`开始，以`}`结束
- 不得添加任何说明文字、介绍语句或总结语句
- 不得使用markdown代码块标记（如\`\`\`json）
- 不得包含任何非JSON格式的内容

<----------------------------(user_prompt)---------------------------->

请基于前序步骤的JSON结构，将其中已识别的CARD控件从简单的content字段转换为结构化的fields字段格式。

### 重要提醒：信息提取准确性是最高优先级要求

**绝对禁止添加任何原始content中不存在的信息！**
**绝对禁止遗漏任何关键的结构化信息！**

### 输入数据
```json
${previous_step_output}
```

### 转换执行要求

#### 1. CARD控件定位（必须首先执行）
- 扫描widgets数组，找到所有type为"CARD"的控件
- 确认每个CARD控件的style属性和content内容
- 确保处理的准确性和完整性

#### 2. 字段结构化提取
- 从content字段的原始文本中提取关键字段信息
- 按照对应CARD类型的字段结构进行准确映射
- 保持原始数据的完整性和准确性

#### 3. 标签和描述生成
- 根据提取的信息生成合适的标签
- 创建简洁明了的subtitle描述
- 确保标签的相关性和价值性

#### 4. 数据验证检查
- [ ] **字段完整性检查**：确保必要字段都已提取
- [ ] **数据格式检查**：验证价格、面积、电话等格式正确
- [ ] **信息一致性检查**：确保提取的信息与原文一致
- [ ] **标签合理性检查**：验证生成的标签准确反映特征

### 输出格式要求

生成完整的DocumentData JSON结构，包含转换后的CARD控件，无```json```标记，纯JSON格式。

### 处理流程

#### 1. 输入验证
- 验证前序步骤输出的数据结构完整性
- 识别widgets数组中的CARD控件
- 检查已生成控件，为标题重复检测做准备

#### 2. 推荐验证与调整
- 逐一验证CARD推荐的合理性
- 基于完整内容重新分析卡片信息特征
- 执行必要的类型或样式调整

#### 3. 字段提取与结构化
- 为验证通过的片段提取结构化字段
- 应用对应CARD类型的字段结构
- 生成合适的标签和描述信息
- 处理标题重复问题
- **编号应用**：使用前序步骤预分配的序列编号，无需计算位置

#### 4. 最终验证
- 检查字段提取的完整性和准确性
- 验证数据格式的正确性
- 确保信息的一致性

#### 5. 输出优化
- 整理最终的控件数组
- 更新处理元数据
- 为最终输出准备数据

## 核心执行要求

1. **推荐验证**：对CARD推荐进行二次验证，确保卡片信息特征明确
2. **字段提取**：准确提取结构化字段，确保信息完整性
3. **数据准确性**：确保所有提取的字段数据准确无误
4. **标签生成**：生成相关性强、价值高的标签
5. **标题重复处理**：处理CARD控件与父级TITLE控件的标题重复
6. **类型调整记录**：记录所有类型调整的原因和依据
7. **信息传递**：为最终输出提供完整的处理结果
8. **序列编号应用**：使用前序步骤预分配的序列编号，确保编号正确性
9. **纯JSON输出**：必须输出纯JSON格式，严禁添加任何说明文字或代码块标记
10. **忠实性保证**：严格基于原始content内容进行提取，禁止添加虚构信息

<----------------------------(previous_step_output)---------------------------->


{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"subtitle": "",
"widgets": [
{
"serial": "0",
"type": "TITLE",
"style": "DOCUMENT",
"title": "慧芝湖花园3室2厅2卫价值评测报告"
},
{
"serial": "1",
"type": "TITLE",
"style": "SECTION",
"title": "小区基本信息分析"
},
{
"serial": "1.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "1. 小区户型分析"
},
{
"serial": "1.1.1",
"type": "TEXT",
"style": "BOARD",
"title": "户型评估",
"content": "**户型评估**：小区在售房源以3室户型为主，占比**42.86%**，挂牌均价最高达106,985元/㎡；2室户型价格相对较低，为100,000元/㎡。"
},
{
"serial": "1.1.2",
"type": "CHART",
"style": "BAR",
"title": "在售房源户型对比（元/㎡）",
"cols": ["挂牌均价", "新增挂牌面积"],
"content": [
{
"title": "2室",
"content": [100000, 196]
},
{
"title": "3室",
"content": [106985, 398]
},
{
"title": "4室",
"content": [103667, 300]
}
]
},
{
"serial": "1.1.3",
"type": "CHART",
"style": "LINE",
"title": "小区近12个月挂牌均价走势（元/㎡）",
"cols": ["2024年08月", "2024年09月", "2024年10月", "2024年11月", "2024年12月", "2025年01月", "2025年02月", "2025年03月", "2025年04月", "2025年05月", "2025年06月", "2025年07月"],
"content": [
{
"title": "挂牌均价",
"content": [null, null, 100000, 106473, 105950, 102416, 101960, 109001, 108324, 107222, 103070, 105689]
}
]
},
{
"serial": "1.1.4",
"type": "LIST",
"style": "BOARD",
"title": "趋势分析",
"content": [
{
"title": "",
"content": "挂牌均价呈现波动上升趋势，从2024年10月的100,000元/㎡升至2025年7月的105,689元/㎡"
},
{
"title": "",
"content": "2025年3月达到挂牌均价峰值109,001元/㎡"
},
{
"title": "",
"content": "成交活跃期集中在2024年11-12月，最高单月成交6套"
}
]
},
{
"serial": "1.2",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "2. 板块市场对比分析"
},
{
"serial": "1.2.1",
"type": "CHART",
"style": "LINE",
"title": "板块近12个月挂牌均价走势（元/㎡）",
"cols": ["2024年08月", "2024年09月", "2024年10月", "2024年11月", "2024年12月", "2025年01月", "2025年02月", "2025年03月", "2025年04月", "2025年05月", "2025年06月", "2025年07月"],
"content": [
{
"title": "板块挂牌均价",
"content": [78913, 82594, 82346, 82061, 80577, 77387, 80282, 81956, 78560, 79206, 78951, 76071]
}
]
},
{
"serial": "1.2.2",
"type": "LIST",
"style": "BOARD",
"title": "板块对比分析",
"content": [
{
"title": "",
"content": "小区挂牌均价(105,689元/㎡)显著高于板块平均水平(76,071元/㎡)，溢价约39%"
},
{
"title": "",
"content": "小区成交均价波动较大，2025年2月出现异常低值73,902元/㎡"
},
{
"title": "",
"content": "板块成交高峰出现在2024年12月，单月成交72套"
}
]
},
{
"serial": "2",
"type": "TITLE",
"style": "SECTION",
"title": "区域价值"
},
{
"serial": "2.1",
"type": "TEXT",
"style": "BOARD",
"content": "作为上海市静安区的核心居住板块，慧芝湖花园（三期）坐拥大宁国际商圈优质资源，45%高绿化率营造出都市绿洲般的居住环境。项目由嘉华(中国)投资有限公司开发，龙湖物业提供专业管理（物业费2.7元/月/㎡），完美融合国际化社区品质与便利生活体验。"
},
{
"serial": "2.2",
"type": "LIST",
"style": "BOARD",
"title": "区域核心价值体现于",
"content": [
{
"title": "双轨交枢纽优势",
"content": "步行范围内覆盖1号线马戏城站与多条公交干线"
},
{
"title": "全龄教育资源矩阵",
"content": "1公里内覆盖幼儿园至小学优质教育机构"
},
{
"title": "商业配套集群",
"content": "百联莘荟购物中心等商业体形成5分钟生活圈"
},
{
"title": "生态宜居品质",
"content": "2.5低容积率与板楼设计保障居住舒适度"
}
]
},
{
"serial": "2.3",
"type": "LIST",
"style": "BOARD",
"title": "交通网络",
"content": [
{
"title": "轨交动脉",
"content": "距1号线马戏城站约430米（步行6分钟），快速连接人民广场、徐家汇等核心商圈"
},
{
"title": "公交覆盖",
"content": "广中路平型关路站（305米）汇集107/547/767等8条公交线路，形成辐射全城的交通网络"
},
{
"title": "路网体系",
"content": "平型关路、广中路、共和新路构成三横三纵路网，15分钟车程直达内环高架"
}
]
},
{
"serial": "3",
"type": "TITLE",
"style": "SECTION",
"title": "生活配套"
},
{
"serial": "3.1",
"type": "LIST",
"style": "BULLET",
"title": "医疗旗舰",
"content": [
{
"title": "",
"content": "登特口腔（348米）：专业口腔医疗机构"
},
{
"title": "",
"content": "益丰大药房（166米）：24小时便民药房"
},
{
"title": "",
"content": "赞瞳眼科诊所（500米）：专科眼科服务"
}
]
},
{
"serial": "3.2",
"type": "LIST",
"style": "BULLET",
"title": "商业矩阵",
"content": [
{
"title": "",
"content": "百联莘荟购物中心（500米）：**4.5星评级综合体**，内含盒马奥莱、Tims咖啡等品牌"
},
{
"title": "",
"content": "宝华现代城商业街（489米）：特色餐饮聚集地"
},
{
"title": "",
"content": "百果园（56米）：社区生鲜便利站"
}
]
},
{
"serial": "3.3",
"type": "LIST",
"style": "BULLET",
"title": "休闲图鉴",
"content": [
{
"title": "",
"content": "自然运动·普拉提（433米）：高端健身会所"
},
{
"title": "",
"content": "星巴克（199米）：社区咖啡社交空间"
},
{
"title": "",
"content": "和记小菜（308米）：4.6分评价的本帮菜餐厅"
}
]
},
{
"serial": "4",
"type": "TITLE",
"style": "SECTION",
"title": "教育资源"
},
{
"serial": "4.1",
"type": "TEXT",
"style": "BOARD",
"title": "特色优势",
"content": "形成500米优质教育圈，实现\"出家门即校门\"的便利教育体验，尤其适合年轻家庭置业需求。"
},
{
"serial": "4.2",
"type": "LIST",
"style": "BULLET",
"title": "全龄教育链",
"content": [
{
"title": "",
"content": "大宁国际第二幼儿园（355米）：区级示范园"
},
{
"title": "",
"content": "上海市大宁国际小学（254米）：优质公办教育"
},
{
"title": "",
"content": "静安区大宁路小学（518米）：**历史悠久的重点小学**"
}
]
},
{
"serial": "5",
"type": "TITLE",
"style": "SECTION",
"title": "小区品质"
},
{
"serial": "5.1",
"type": "TEXT",
"style": "BOARD",
"title": "生态美学",
"content": "中央景观带与组团绿化相结合，45%绿化率打造花园式社区，建筑间距达行业高标准"
},
{
"serial": "5.2",
"type": "TEXT",
"style": "BOARD",
"title": "服务标准",
"content": "龙湖物业提供星级服务，配备3494个停车位（车位比1:0.7），实行人车分流管理"
},
{
"serial": "5.3",
"type": "TEXT",
"style": "BOARD",
"title": "生活场景",
"content": "晨间可步行至星巴克享用早餐，下午在社区园林散步，晚间步行5分钟即达购物中心，完美演绎静安高品质生活范式"
},
{
"serial": "5.4",
"type": "LIST",
"style": "BULLET",
"title": "建筑基因",
"content": [
{
"title": "",
"content": "纯板楼设计（2004-2009年建成）"
},
{
"title": "",
"content": "主力户型72-174㎡（1-4房）"
},
{
"title": "",
"content": "2.5容积率保障低密度居住体验"
}
]
},
{
"serial": "6",
"type": "TITLE",
"style": "SECTION",
"title": "专业服务推荐"
},
{
"serial": "6.1",
"type": "CARD",
"style": "BROKER",
"title": "置业顾问推荐",
"content": {
"name": "张经理",
"phone": "138-0000-1234",
"experience": "8年",
"specialty": "静安区房产投资、学区房置业",
"service_area": "大宁板块、凉城板块",
"rating": "4.8分（基于126条评价）",
"achievement": "累计成交房源超过200套，专注中高端住宅"
}
},
{
"serial": "6.2",
"type": "CARD",
"style": "HOUSING",
"title": "优质房源推荐",
"content": {
"type": "3室2厅2卫",
"area": "110㎡",
"floor": "15/18层",
"orientation": "朝南",
"decoration": "精装修",
"total_price": "1073万",
"unit_price": "97,545元/㎡",
"property_type": "商品房",
"tags": ["学区房", "地铁房", "满五唯一"]
}
},
{
"serial": "6.3",
"type": "CARD",
"style": "COMMUNITY",
"title": "社区配套介绍",
"content": {
"build_year": "2004-2009年",
"property_company": "龙湖物业",
"property_fee": "2.7元/月/㎡",
"greening_rate": "45%",
"plot_ratio": "2.5",
"parking": "3494个车位，车位比1:0.7",
"facilities": ["中央景观带", "健身会所", "24小时安保", "人车分流系统"]
}
},
{
"serial": "0.1",
"type": "LIST",
"style": "BOARD",
"title": "报告基本信息",
"content": [
{
"title": "数据来源",
"content": "上海市房地产交易平台"
},
{
"title": "评测时间",
"content": "2025年7月"
},
{
"title": "平均价格概览",
"content": "**97,600元/㎡**"
}
]
},
{
"serial": "0.2",
"type": "TABLE",
"style": "BOARD",
"title": "评测房源基本信息",
"cols": ["项目", "详情"],
"content": [
[
{"type": "TEXT", "content": "城市"},
{"type": "TEXT", "content": "上海市"}
],
[
{"type": "TEXT", "content": "小区名称"},
{"type": "TEXT", "content": "慧芝湖花园"}
],
[
{"type": "TEXT", "content": "户型"},
{"type": "TEXT", "content": "3室2厅2卫"}
],
[
{"type": "TEXT", "content": "建筑面积"},
{"type": "TEXT", "content": "110㎡"}
],
[
{"type": "TEXT", "content": "朝向"},
{"type": "TEXT", "content": "朝南"}
],
[
{"type": "TEXT", "content": "预估单价"},
{"type": "TEXT", "content": "97,600元/㎡"}
],
[
{"type": "TEXT", "content": "板块位置"},
{"type": "TEXT", "content": "凉城（挂牌板块：大宁板块）"}
]
]
}
]
}
