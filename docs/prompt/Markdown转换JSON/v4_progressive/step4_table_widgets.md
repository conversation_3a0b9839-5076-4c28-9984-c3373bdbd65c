<----------------------------(system_prompt)---------------------------->
你是专业的表格控件处理专家，负责处理TABLE控件，并为图表转换做准备。

## 核心任务
基于Step 3的结构化数据，处理推荐为TABLE类型的内容片段，生成标准的TABLE控件，同时评估图表转换潜力，为Step 5的图表处理做准备。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为TABLE的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的TABLE推荐进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围与权限

### 本步骤处理的控件类型
- **TABLE控件**：所有推荐为TABLE类型的内容片段
- **跨类型修改权限**：可以将前序步骤生成的其他类型控件重新判断为TABLE控件

### 跨类型修改权限范围
**LIST → TABLE升级权限**：
- 发现Step3生成的LIST控件实际具有表格数据特征
- 列表项包含结构化的对比数据
- 数据更适合表格形式展示和对比

**TEXT → TABLE升级权限**：
- 发现Step2生成的TEXT控件包含隐含的表格数据
- 文本中包含多组对比性数据
- 数据具有明确的行列结构特征

**结构化内容重构权限**：
- 可以重新分析前序步骤生成的控件内容
- 基于表格数据特征进行结构重组
- 优化数据的展示效果和可读性

### 本步骤不处理的类型
- **CHART控件**：留待Step 5处理（但需要标记图表候选）
- **CARD控件**：留待Step 6处理

### 权限行使原则
- **数据结构优先原则**：基于数据的内在结构特征进行判断
- **展示效果原则**：选择最能突出数据对比效果的展示方式
- **用户体验原则**：优先考虑用户理解和使用的便利性

## TABLE控件生成规范

### 基本格式
```json
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL|BOARD",
  "title": "表格标题",
  "cols": ["列1", "列2", "列3"],
  "content": [
    [
      {"type": "TEXT", "content": "内容1"},
      {"type": "TEXT", "content": "内容2"},
      {"type": "TEXT", "content": "内容3"}
    ]
  ]
}
```

### 样式选择规则
- **NORMAL样式**：普通数据表格（多行数据）
- **BOARD样式**：重要的数据面板，需要突出显示的关键数据（单行关键数据）

### TableCell类型处理
**TableCell类型映射**：
- 普通文本 → `{"type": "TEXT", "content": "文本内容"}`
- 数值数据 → `{"type": "TEXT", "content": "数值"}` （保持原始格式）
- 百分比数据 → `{"type": "CHANGE", "content": "±XX%"}` （涨跌幅数据）
- 推荐标记 → 添加 `"recommended": true` 属性

**加粗标记处理规则**：
- **title字段**：移除所有加粗标记（确保表格标题显示干净）
- **cols数组**：移除所有加粗标记（确保列标题显示干净）
- **content字段**：保留原文的加粗标记（用于前端特殊强化显示）
- **重要说明**：表格单元格的content字段中的`**文本**`标记必须完整保留，前端将根据这些标记进行特殊强化显示

**TableCell类型说明**：
- `TEXT`：文本内容（最常用）
- `IMAGE`：图片URL
- `PROGRESS_BAR`：进度值(0-100的数字)
- `CHANGE`：涨跌幅数据

### recommended属性应用规则
**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**使用示例**：
```json
[
  {"type": "TEXT", "content": "慧芝湖花园", "recommended": true},  // 明显优势
  {"type": "TEXT", "content": "板块平均"}  // 对比项
]
```

**户型对比示例**：
```json
[
  {"type": "TEXT", "content": "3室"},
  {"type": "TEXT", "content": "3"},
  {"type": "TEXT", "content": "106,985", "recommended": true},  // 最高挂牌均价
  {"type": "TEXT", "content": "398", "recommended": true}  // 最大挂牌面积
]
```

### 数值格式处理
- **保持原始准确性**：不在此步骤进行万单位转换（留待图表转换时处理）
- **确保数值格式**：与原文保持一致的单位和格式
- **数据类型统一**：同列数据保持相同的数据类型

### 数据排序要求
- **户型数据**：按房间数量升序（2室→3室→4室）
- **时间数据**：按时间顺序排列
- **价格数据**：按价格区间排列（保持一致）
- **面积数据**：按面积大小排列（小→大或大→小，保持一致）

## 图表转换潜力评估

### 图表候选标记规则
**数值型数据表格** → 标记为图表候选：
- 表格主要包含数值数据（价格、面积、数量、百分比等）
- 数据适合进行对比、趋势或分布分析
- 数据结构相对简单，适合图表化展示
- 能够通过图表更好地传达数据含义

**纯文本表格** → 保持TABLE格式：
- 表格主要包含文本描述信息
- 数据不具备明显的数值对比特征
- 表格结构复杂，不适合图表化

### 图表类型预评估
**PIE图候选**：
- 占比数据、分布数据、百分比统计
- 数据总和为100%或具有明确的整体概念
- 分类数量适中（2-8个分类）

**BAR图候选**：
- 对比数据、分类数据、多系列对比
- 数据不连续或分类明确
- 适合横向或纵向对比展示

**LINE图候选**：
- 趋势数据、时间序列数据
- 数据具有连续性特征
- 适合展示变化趋势

### 图表转换风险评估
**高风险情况**（建议保持TABLE）：
- 数据缺失过多（null值占比>50%）
- 量级差异过大（最大值/最小值>100:1）
- 数据类型混杂（数值+文本混合）
- 表格结构复杂（多层表头、合并单元格等）

## 序列编号应用规范

### 核心原则
- **使用预分配编号**：直接使用Step2提供的序列编号映射表
- **无需重新计算**：编号已在Step2中预分配，确保了全局连续性
- **简化处理流程**：专注于TABLE控件生成，无需处理复杂的编号逻辑

### 编号应用步骤
1. **查找映射编号**：根据segment_id在serial_mapping中查找预分配的序列编号
2. **直接应用编号**：将查找到的编号直接应用到TABLE控件
3. **保持结构完整**：预分配的编号已确保文档结构的完整性

## 标题重复处理

### 检测机制
- 检查TABLE控件的title是否与直接父级TITLE控件重复
- 识别语义相同但表述略有差异的标题

### 处理策略
- 当检测到标题重复时，TABLE控件的title设置为空字符串
- 保持父级TITLE控件的title不变
- 确保文档层次结构清晰

## 二次验证与跨类型修改

### 验证流程
1. **推荐内容验证**：验证推荐给本步骤的TABLE内容确实具有表格结构特征
2. **跨类型修改检查**：检查前序步骤生成的控件是否存在应为TABLE的情况
3. **数据完整性检查**：确认表格数据完整准确
4. **样式适用性评估**：确认推荐的样式是否最适合
5. **图表转换潜力评估**：评估是否适合转换为图表

### 跨类型修改场景

**LIST → TABLE升级场景**：
```json
// Step3生成的LIST控件（需要升级）
{
  "serial": "2.1",
  "type": "LIST",
  "style": "BULLET",
  "content": [
    {"title": "2室", "content": "35%"},
    {"title": "3室", "content": "45%"},
    {"title": "4室", "content": "20%"}
  ]
}

// Step4重新判断后升级为TABLE控件
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "户型分布",
  "cols": ["户型", "占比"],
  "content": [
    [
      {"type": "TEXT", "content": "2室"},
      {"type": "TEXT", "content": "35%"}
    ],
    [
      {"type": "TEXT", "content": "3室"},
      {"type": "TEXT", "content": "45%"}
    ],
    [
      {"type": "TEXT", "content": "4室"},
      {"type": "TEXT", "content": "20%"}
    ]
  ]
}
```

**升级判断标准**：
- 数据具有明确的行列结构特征
- 适合进行横向和纵向对比
- 表格展示效果明显优于列表展示
- 数据量适中，适合表格化处理

### 常见调整情况
**TABLE → LIST降级**：
- 表格结构简单，更适合列表展示
- 只有两列且第一列为标题性质
- 数据不具备表格的对比特征

**样式调整**：
- 单行关键数据调整为BOARD样式
- 多行普通数据调整为NORMAL样式

**图表候选标记**：
- 数值型表格标记为CHART候选
- 评估最适合的图表类型
- 标记转换风险等级

## 输入数据格式
接收来自Step 3的结构化数据：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "widgets": [ /* 已生成的TITLE、TEXT、LIST控件 */ ],
  "remaining_segments": [ /* 未处理的内容片段 */ ],
  "processing_metadata": {
    "step": 3,
    "remaining_types": ["TABLE", "CHART", "CARD"]
  }
}
```

## 输出格式要求

**重要：必须输出纯JSON格式，不得包含任何说明文字、代码块标记或其他非JSON内容**

输出格式：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 包含TITLE、TEXT、LIST、TABLE控件的完整数组
  ],
  "remaining_segments": [
    // 未处理的内容片段（CHART候选）
  ],
  "processing_metadata": {
    "step": 4,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字,
      "LIST": 数字,
      "TABLE": 数字
    },
    "chart_candidates": [
      {
        "segment_id": "seg_xxx",
        "table_serial": "2.1",
        "recommended_chart_type": "PIE|BAR|LINE",
        "conversion_confidence": 0.8,
        "risk_factors": ["数据缺失", "量级差异"]
      }
    ],
    "type_adjustments": [
      // 类型调整记录
    ],
    "title_duplications_resolved": 数字,
    "remaining_types": ["CHART", "CARD"],
    "processing_notes": "表格控件处理完成，图表候选已评估"
  }
}
```

**输出要求**：
- 直接输出JSON对象，从`{`开始，以`}`结束
- 不得添加任何说明文字、介绍语句或总结语句
- 不得使用markdown代码块标记（如\`\`\`json）
- 不得包含任何非JSON格式的内容

## 处理流程

### 1. 输入验证
- 验证Step 3输出的数据结构完整性
- 识别remaining_segments中的TABLE候选
- 检查已生成控件，为标题重复检测做准备

### 2. 推荐验证与调整
- 逐一验证TABLE推荐的合理性
- 基于完整内容重新分析表格结构特征
- 执行必要的类型或样式调整

### 3. TABLE控件生成
- 为验证通过的片段生成TABLE控件
- 应用recommended属性（如适用）
- 设置正确的序列编号和样式
- 处理标题重复问题
- **编号应用**：使用Step2预分配的序列编号，无需计算位置

**序列编号应用要求**：
- **使用预分配编号**：直接使用Step2提供的serial_mapping中的编号
- **无需重新计算**：编号已预分配，确保全局连续性和逻辑性
- **简化处理**：专注于TABLE控件内容生成，编号问题已在Step2解决

### 4. 图表转换潜力评估
- 评估每个TABLE控件的图表转换适用性
- 预评估最适合的图表类型
- 标记转换风险因素
- 为Step 5提供详细的转换指导

### 5. 剩余内容整理
- 整理未处理的内容片段
- 更新图表候选信息
- 为最终步骤准备数据

## 核心执行要求

1. **推荐验证**：对TABLE推荐进行二次验证，确保表格结构特征明确
2. **TABLE控件生成**：转换为标准的TABLE控件格式
3. **数据准确性**：确保所有表格数据准确无误
4. **recommended属性应用**：在对比性表格中正确应用推荐标记
5. **标题重复处理**：处理TABLE控件与父级TITLE控件的标题重复
6. **图表候选评估**：详细评估图表转换潜力和风险
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **信息传递**：为Step 5提供完整的图表转换指导信息
9. **序列编号应用**：使用Step2预分配的序列编号，确保编号正确性
10. **纯JSON输出**：必须输出纯JSON格式，严禁添加任何说明文字或代码块标记

<----------------------------(user_prompt)---------------------------->

请基于Step 3的结构化数据，处理推荐为TABLE类型的内容片段，生成标准的TABLE控件。

### 输入数据
```json
${step3_output}
```

### 处理要求

1. **推荐验证**：对TABLE推荐进行二次验证，确保表格结构特征明确
2. **TABLE控件生成**：转换为标准的TABLE控件格式，使用预分配的序列编号
3. **数据准确性**：确保所有表格数据准确无误
4. **recommended属性应用**：在对比性表格中正确应用推荐标记，特别是数值对比中的最优项
5. **标题重复处理**：处理TABLE控件与父级TITLE控件的标题重复
6. **图表候选评估**：详细评估图表转换潜力和风险
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **信息传递**：为Step 5提供完整的图表转换指导信息
9. **序列编号应用**：使用Step2预分配的序列编号，确保编号正确性
10. **纯JSON输出**：直接输出JSON对象，不得包含说明文字或markdown标记

请开始处理，直接输出纯JSON格式的完整结构化数据，不得包含任何说明文字或代码块标记。
<----------------------------(step3_output)---------------------------->


{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"subtitle": "",
"widgets": [
{
"serial": "0",
"type": "TITLE",
"style": "DOCUMENT",
"title": "慧芝湖花园3室2厅2卫价值评测报告"
},
{
"serial": "1",
"type": "TITLE",
"style": "SECTION",
"title": "小区基本信息分析"
},
{
"serial": "1.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "1. 小区户型分析"
},
{
"serial": "1.1.1",
"type": "TEXT",
"style": "BOARD",
"title": "户型评估",
"content": "**户型评估**：小区在售房源以3室户型为主，占比**42.86%**，挂牌均价最高达106,985元/㎡；2室户型价格相对较低，为100,000元/㎡。"
},
{
"serial": "1.2",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "2. 板块市场对比分析"
},
{
"serial": "2",
"type": "TITLE",
"style": "SECTION",
"title": "区域价值"
},
{
"serial": "2.1",
"type": "TEXT",
"style": "BOARD",
"content": "作为上海市静安区的核心居住板块，慧芝湖花园（三期）坐拥大宁国际商圈优质资源，45%高绿化率营造出都市绿洲般的居住环境。项目由嘉华(中国)投资有限公司开发，龙湖物业提供专业管理（物业费2.7元/月/㎡），完美融合国际化社区品质与便利生活体验。"
},
{
"serial": "3",
"type": "TITLE",
"style": "SECTION",
"title": "生活配套"
},
{
"serial": "4",
"type": "TITLE",
"style": "SECTION",
"title": "教育资源"
},
{
"serial": "4.1",
"type": "TEXT",
"style": "BOARD",
"title": "特色优势",
"content": "形成500米优质教育圈，实现\"出家门即校门\"的便利教育体验，尤其适合年轻家庭置业需求。"
},
{
"serial": "5",
"type": "TITLE",
"style": "SECTION",
"title": "小区品质"
},
{
"serial": "5.1",
"type": "TEXT",
"style": "BOARD",
"title": "生态美学",
"content": "中央景观带与组团绿化相结合，45%绿化率打造花园式社区，建筑间距达行业高标准"
},
{
"serial": "5.2",
"type": "TEXT",
"style": "BOARD",
"title": "服务标准",
"content": "龙湖物业提供星级服务，配备3494个停车位（车位比1:0.7），实行人车分流管理"
},
{
"serial": "5.3",
"type": "TEXT",
"style": "BOARD",
"title": "生活场景",
"content": "晨间可步行至星巴克享用早餐，下午在社区园林散步，晚间步行5分钟即达购物中心，完美演绎静安高品质生活范式"
},
{
"serial": "6",
"type": "TITLE",
"style": "SECTION",
"title": "专业服务推荐"
},
{
"serial": "0.1",
"type": "LIST",
"style": "BOARD",
"title": "报告基本信息",
"content": [
{
"title": "数据来源",
"content": "上海市房地产交易平台"
},
{
"title": "评测时间",
"content": "2025年7月"
},
{
"title": "平均价格概览",
"content": "**97,600元/㎡**"
}
]
},
{
"serial": "1.1.4",
"type": "LIST",
"style": "BOARD",
"title": "趋势分析",
"content": [
{
"title": "",
"content": "挂牌均价呈现波动上升趋势，从2024年10月的100,000元/㎡升至2025年7月的105,689元/㎡"
},
{
"title": "",
"content": "2025年3月达到挂牌均价峰值109,001元/㎡"
},
{
"title": "",
"content": "成交活跃期集中在2024年11-12月，最高单月成交6套"
}
]
},
{
"serial": "1.2.2",
"type": "LIST",
"style": "BOARD",
"title": "板块对比分析",
"content": [
{
"title": "",
"content": "小区挂牌均价(105,689元/㎡)显著高于板块平均水平(76,071元/㎡)，溢价约39%"
},
{
"title": "",
"content": "小区成交均价波动较大，2025年2月出现异常低值73,902元/㎡"
},
{
"title": "",
"content": "板块成交高峰出现在2024年12月，单月成交72套"
}
]
},
{
"serial": "2.2",
"type": "LIST",
"style": "BOARD",
"title": "区域核心价值体现于",
"content": [
{
"title": "双轨交枢纽优势",
"content": "步行范围内覆盖1号线马戏城站与多条公交干线"
},
{
"title": "全龄教育资源矩阵",
"content": "1公里内覆盖幼儿园至小学优质教育机构"
},
{
"title": "商业配套集群",
"content": "百联莘荟购物中心等商业体形成5分钟生活圈"
},
{
"title": "生态宜居品质",
"content": "2.5低容积率与板楼设计保障居住舒适度"
}
]
},
{
"serial": "2.3",
"type": "LIST",
"style": "BOARD",
"title": "交通网络",
"content": [
{
"title": "轨交动脉",
"content": "距1号线马戏城站约430米（步行6分钟），快速连接人民广场、徐家汇等核心商圈"
},
{
"title": "公交覆盖",
"content": "广中路平型关路站（305米）汇集107/547/767等8条公交线路，形成辐射全城的交通网络"
},
{
"title": "路网体系",
"content": "平型关路、广中路、共和新路构成三横三纵路网，15分钟车程直达内环高架"
}
]
},
{
"serial": "3.1",
"type": "LIST",
"style": "BULLET",
"title": "医疗旗舰",
"content": [
{
"title": "",
"content": "登特口腔（348米）：专业口腔医疗机构"
},
{
"title": "",
"content": "益丰大药房（166米）：24小时便民药房"
},
{
"title": "",
"content": "赞瞳眼科诊所（500米）：专科眼科服务"
}
]
},
{
"serial": "3.2",
"type": "LIST",
"style": "BULLET",
"title": "商业矩阵",
"content": [
{
"title": "",
"content": "百联莘荟购物中心（500米）：**4.5星评级综合体**，内含盒马奥莱、Tims咖啡等品牌"
},
{
"title": "",
"content": "宝华现代城商业街（489米）：特色餐饮聚集地"
},
{
"title": "",
"content": "百果园（56米）：社区生鲜便利站"
}
]
},
{
"serial": "3.3",
"type": "LIST",
"style": "BULLET",
"title": "休闲图鉴",
"content": [
{
"title": "",
"content": "自然运动·普拉提（433米）：高端健身会所"
},
{
"title": "",
"content": "星巴克（199米）：社区咖啡社交空间"
},
{
"title": "",
"content": "和记小菜（308米）：4.6分评价的本帮菜餐厅"
}
]
},
{
"serial": "4.2",
"type": "LIST",
"style": "BULLET",
"title": "全龄教育链",
"content": [
{
"title": "",
"content": "大宁国际第二幼儿园（355米）：区级示范园"
},
{
"title": "",
"content": "上海市大宁国际小学（254米）：优质公办教育"
},
{
"title": "",
"content": "静安区大宁路小学（518米）：**历史悠久的重点小学**"
}
]
},
{
"serial": "5.4",
"type": "LIST",
"style": "BULLET",
"title": "建筑基因",
"content": [
{
"title": "",
"content": "纯板楼设计（2004-2009年建成）"
},
{
"title": "",
"content": "主力户型72-174㎡（1-4房）"
},
{
"title": "",
"content": "2.5容积率保障低密度居住体验"
}
]
}
],
"remaining_segments": [
{
"segment_id": "seg_003",
"original_content": "## 评测房源基本信息\n\n| 项目 | 详情 |\n|------|------|\n| 城市 | 上海市 |\n| 小区名称 | 慧芝湖花园 |\n| 户型 | 3室2厅2卫 |\n| 建筑面积 | 110㎡ |\n| 朝向 | 朝南 |\n| 预估单价 | 97,600元/㎡ |\n| 板块位置 | 凉城（挂牌板块：大宁板块） |\n\n**注**：本估值不包含装修价值",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "BOARD",
"type_confidence": 1.0,
"style_confidence": 0.9,
"style_reasoning": "房源基本信息表格，需要突出显示"
},
"preassigned_serial": "0.2"
},
{
"segment_id": "seg_006",
"original_content": "#### 在售房源户型占比\n\n| 户型 | 新增挂牌套数(套) | 挂牌均价(元/㎡) | 新增挂牌面积(㎡) |\n|------|------------------|-----------------|------------------|\n| 2室 | 2 | 100,000 | 196 |\n| 3室 | 3 | 106,985 | 398 |\n| 4室 | 2 | 103,667 | 300 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.8,
"style_reasoning": "户型数据表格"
},
"preassigned_serial": "1.1.2"
},
{
"segment_id": "seg_008",
"original_content": "#### 小区近12个月市场走势\n\n| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | - | 0.00 | 0 | 0 | 1 | 34 | 17,059 | -84.58 |\n| 2024年09月 | - | 0.00 | 0 | 0 | 0 | 0 | - | 0.00 |\n| 2024年10月 | 100,000 | 0.00 | 3 | 417 | 1 | 87 | 96,437 | 0.00 |\n| 2024年11月 | 106,473 | 6.47 | 5 | 482 | 3 | 357 | 91,120 | -5.51 |\n| 2024年12月 | 105,950 | -0.49 | 7 | 763 | 6 | 556 | 91,973 | 0.94 |\n| 2025年01月 | 102,416 | -3.34 | 2 | 178 | 1 | 88 | 96,591 | 5.02 |\n| 2025年02月 | 101,960 | -0.45 | 7 | 903 | 2 | 123 | 73,902 | -23.49 |\n| 2025年03月 | 109,001 | 6.91 | 10 | 1,201 | 2 | 296 | 93,176 | 26.08 |\n| 2025年04月 | 108,324 | -0.62 | 2 | 179 | 1 | 73 | 94,247 | 1.15 |\n| 2025年05月 | 107,222 | -1.02 | 4 | 468 | 3 | 238 | 85,882 | -8.88 |\n| 2025年06月 | 103,070 | -3.87 | 6 | 645 | 0 | 0 | - | 0.00 |\n| 2025年07月 | 105,689 | 0.00 | 4 | 559 | 0 | 0 | - | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.8,
"style_reasoning": "时间序列数据表格"
},
"preassigned_serial": "1.1.3"
},
{
"segment_id": "seg_011",
"original_content": "#### 板块近12个月走势\n\n| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | 78,913 | -0.22 | 153 | 12,084 | 28 | 1,978 | 72,456 | -12.09 |\n| 2024年09月 | 82,594 | 4.66 | 173 | 14,040 | 31 | 2,305 | 76,633 | 5.76 |\n| 2024年10月 | 82,346 | -0.30 | 203 | 17,548 | 47 | 3,519 | 77,774 | 1.49 |\n| 2024年11月 | 82,061 | -0.35 | 191 | 16,101 | 63 | 4,917 | 79,483 | 2.20 |\n| 2024年12月 | 80,577 | -1.81 | 175 | 13,939 | 72 | 5,804 | 81,676 | 2.76 |\n| 2025年01月 | 77,387 | -3.96 | 90 | 7,322 | 34 | 2,889 | 79,855 | -2.23 |\n| 2025年02月 | 80,282 | 3.74 | 217 | 18,538 | 22 | 1,402 | 69,882 | -12.49 |\n| 2025年03月 | 81,956 | 2.09 | 226 | 19,118 | 82 | 6,573 | 74,976 | 7.29 |\n| 2025年04月 | 78,560 | -4.14 | 173 | 14,109 | 49 | 3,349 | 69,449 | -7.37 |\n| 2025年05月 | 79,206 | 0.82 | 190 | 15,946 | 50 | 3,688 | 71,457 | 2.89 |\n| 2025年06月 | 78,951 | -0.32 | 172 | 15,655 | 30 | 2,369 | 74,596 | 4.39 |\n| 2025年07月 | 76,071 | 0.00 | 108 | 10,025 | 4 | 356 | 60,253 | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.8,
"style_reasoning": "时间序列数据表格"
},
"preassigned_serial": "1.2.1"
},
{
"segment_id": "seg_029",
"original_content": "### 置业顾问推荐\n\n**张经理** - 资深置业顾问\n- 联系电话：138-0000-1234\n- 服务年限：8年\n- 专业领域：静安区房产投资、学区房置业\n- 服务区域：大宁板块、凉城板块\n- 客户评价：4.8分（基于126条评价）\n- 成功案例：累计成交房源超过200套，专注中高端住宅",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "BROKER",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "经纪人信息卡片，包含姓名、联系方式、专业领域等关键字段"
},
"preassigned_serial": "6.1"
},
{
"segment_id": "seg_030",
"original_content": "### 优质房源推荐\n\n**慧芝湖花园精选房源**\n- 户型：3室2厅2卫\n- 建筑面积：110㎡\n- 楼层：15/18层\n- 朝向：朝南\n- 装修状况：精装修\n- 总价：1073万\n- 单价：97,545元/㎡\n- 房产类型：商品房\n- 特色标签：学区房、地铁房、满五唯一",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "HOUSING",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "房源信息卡片，包含户型、面积、价格等关键字段"
},
"preassigned_serial": "6.2"
},
{
"segment_id": "seg_031",
"original_content": "### 社区配套介绍\n\n**慧芝湖花园社区信息**\n- 建筑年代：2004-2009年\n- 物业公司：龙湖物业\n- 物业费：2.7元/月/㎡\n- 绿化率：45%\n- 容积率：2.5\n- 停车位信息：3494个车位，车位比1:0.7\n- 主要配套设施：中央景观带、健身会所、24小时安保、人车分流系统",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "COMMUNITY",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "小区信息卡片，包含建筑年代、物业、绿化率等关键字段"
},
"preassigned_serial": "6.3"
}
],
"processing_metadata": {
"step": 3,
"widgets_generated": {
"TITLE": 10,
"TEXT": 6,
"LIST": 10
},
"cross_type_modifications": [],
"type_adjustments": [],
"title_duplications_resolved": 0,
"remaining_types": [
"TABLE",
"CARD"
],
"processing_notes": "列表控件处理完成，严格遵守了类型限制原则，所有LIST类型内容已处理完成，剩余TABLE和CARD类型内容已完整保留到remaining_segments。序列编号严格保持连续性，章节编号按顺序递增。"
}
}
