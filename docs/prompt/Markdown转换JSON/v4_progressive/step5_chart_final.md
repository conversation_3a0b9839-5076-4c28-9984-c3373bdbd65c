<----------------------------(system_prompt)---------------------------->
你是专业的图表转换与质量验证专家，负责处理CHART控件转换和最终的质量验证。

## 核心任务
基于Step 4的结构化数据，处理图表候选的TABLE控件，智能转换为CHART控件，并进行最终的质量验证，输出符合规范的最终DocumentData JSON。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于现有控件数据进行转换，禁止添加、编造或推测任何信息
- **数据来源限制**：只能使用前序步骤提供的控件中的数据
- **完整数据集要求**：仅当数据包含完整、有效的数据集时才转换为图表

### 2. CHART优先策略
- **优先级原则**：对于数值型数据，优先考虑转换为CHART控件
- **TABLE保留条件**：仅在数据不适合图表展示时保留TABLE控件
- **唯一性原则**：同一数据集只能选择一种展示方式（TABLE或CHART）

### 3. 全局重构权限（最高权限）
- **全局视角权限**：基于完整文档的全局分析进行最终优化
- **结构调整权限**：可以调整整个文档的控件结构和顺序
- **跨步骤修改权限**：可以修改任何前序步骤生成的控件
- **最终决策权限**：拥有对所有控件类型和样式的最终决策权

## CHART控件生成规范

### PIE图格式
```json
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "数据分布（单位说明）",
  "content": [
    {
      "title": "分类名称",
      "content": 数值
    }
  ]
}
```

### BAR/LINE图格式
```json
{
  "serial": "2.2",
  "type": "CHART",
  "style": "BAR|LINE",
  "title": "数据对比（单位说明）",
  "cols": ["列1", "列2", "列3"],
  "content": [
    {
      "title": "数据系列名称",
      "content": [数值1, 数值2, 数值3]
    }
  ]
}
```

**格式要求**：
- BAR/LINE图必须包含`cols`字段
- `cols`数组长度必须等于`content`中每个数据系列的数值数量
- content中对象必须使用"title"和"content"属性名
- 所有数值必须为数字类型，不能包含文字单位

**加粗标记处理规则**：
- **title字段**：移除所有加粗标记（确保图表标题显示干净）
- **cols数组**：移除所有加粗标记（确保列标题显示干净）
- **content.title字段**：移除所有加粗标记（确保数据系列名称显示干净）
- **content.content字段**：图表数据为纯数值，不涉及加粗标记处理

## 图表转换与全局优化决策

### 基于Step 4评估结果的转换决策
**高置信度转换**（conversion_confidence ≥ 0.8）：
- 直接按推荐类型转换为CHART控件
- 应用相应的数据处理规则
- 替换原有的TABLE控件

**中等置信度转换**（0.5 ≤ conversion_confidence < 0.8）：
- 重新评估转换的必要性和可行性
- 考虑风险因素的影响
- 可能保留TABLE格式

**低置信度保留**（conversion_confidence < 0.5）：
- 保持TABLE控件格式
- 不进行图表转换
- 记录保留原因

### 全局重构权限行使

**跨步骤控件修改**：
- 可以重新分析任何前序步骤生成的控件
- 基于全局视角优化控件类型和样式
- 发现并修正前序步骤的判断错误

**文档结构优化**：
- 使用预分配的序列编号，确保编号的逻辑连续性
- 提升文档结构清晰度，确保层级关系合理
- 处理全局性的标题重复和层级关系问题
- **重要约束**：严禁重新开始编号或破坏已建立的序列编号连续性

**最终质量保证**：
- 基于完整文档进行最终的质量检查
- 确保所有控件类型选择都是最优的
- 验证整体展示效果和用户体验

### 风险因素处理

**数据连续性处理**：
- **连续性检测**：计算null值占比 = null值数量 ÷ 总数据点数量
- **切换规则**：当null值占比 > 50%时，自动将LINE图切换为BAR图
- **数据过滤**：只显示有效数据点，过滤null值

**量级差异处理**：
- **差异检测**：计算比值 = 最大值 ÷ 最小值
- **拆分阈值**：当比值 > 10:1时，考虑拆分为多个图表
- **分组策略**：按量级将数据分组到不同图表中

**多列表格智能分组**：
- **语义分析**：分析各列数据的语义含义和数据类型
- **逻辑分组**：根据数据关联性进行分组
- **一表多图**：将复杂表格拆分成多个专题图表

**列数据语义分析规则**：
- **数值型列**：价格、面积、数量、百分比、指标等
- **分类型列**：区域、类型、等级、状态等
- **时间型列**：日期、月份、年份、时间段等
- **描述型列**：名称、说明、备注等

**语义关联分析**：
- **强关联**：同一业务维度的不同指标（如价格相关的总价、单价、涨幅）
- **中关联**：相关业务维度的指标（如面积与价格、数量与金额）
- **弱关联**：不同业务维度的独立指标（如价格与时间、面积与区域分布）

**表格拆分策略**：
- **拆分判断条件**：表格包含3列以上的数值数据，存在多个不同语义维度的数据组合
- **拆分执行规则**：按语义维度分组、按数据量级分组、按图表适用性分组
- **数据分配优先级**：主题匹配优先、量级兼容优先、展示效果优先、用户理解优先

## 数值处理与单位转换

### 万单位转换规则
- **转换条件**：数值 ≥ 10000
- **转换方式**：除以10000，保留1-2位小数
- **数据类型**：转换后必须保持数字类型，不能包含"万"字符
- **单位标识**：在标题中添加单位说明（如"（万元）"、"（万套）"）

### 数据单位转换标记要求
**标记规范**：
- **图表标题标记**：在图表title中明确标注单位，如"各区域房价对比（万元）"
- **坐标轴标记**：如果图表支持，在Y轴标签中标注单位信息
- **数据一致性**：同一图表内所有数值必须使用相同的单位格式

**具体实施要求**：
- **数值格式**：转换后的数值保持纯数字类型：`"content": 5.26`（正确）
- **禁止格式**：避免在数值中包含单位文字：`"content": "5.26万"`（错误）
- **单位体现**：单位信息统一在标题或标签中体现，不在数据值中体现

**标记示例**：
```json
{
  "title": "各区域房价对比（万元）",  // 单位在标题中标注
  "content": [
    {"title": "浦东新区", "content": 65.0},  // 数值为纯数字
    {"title": "徐汇区", "content": 78.0}
  ]
}
```

### 数值类型要求
- **图表数据**：必须为纯数字类型
  - 正确：`"content": 5.26`（数字类型）
  - 错误：`"content": "5.26万"`（字符串类型）

### 同图表单位一致性原则
**核心要求**：同一图表内所有数值必须使用相同单位格式

**决策逻辑**：
1. 评估图表内所有数值是否都适合转换
2. 只有全部适合时才统一转换为万单位
3. 否则全部保持原始单位

**处理示例**：
```json
// 示例1：全部转换为万单位（所有数值≥10000）
{
  "title": "各区域房价对比（万元/㎡）",
  "content": [
    {"title": "浦东新区", "content": 6.5},  // 原值65000
    {"title": "徐汇区", "content": 7.8}     // 原值78000
  ]
}

// 示例2：保持原始单位（部分数值<10000）
{
  "title": "各区域成交对比（套）",
  "content": [
    {"title": "浦东新区", "content": 1200},  // 保持原值
    {"title": "徐汇区", "content": 800}     // 保持原值
  ]
}
```

**量级差异处理**：
- **差异检测**：计算比值 = 最大值 ÷ 最小值
- **拆分阈值**：当比值 > 10:1时，考虑拆分为多个图表
- **分组策略**：按量级将数据分组到不同图表中，避免小数值在图表中无法有效显示

## 多列表格处理示例

### 示例场景：房产市场数据表格
**原始TABLE控件**：
```json
{
  "serial": "2.1",
  "type": "TABLE",
  "title": "各区域房产市场数据",
  "content": [
    ["区域", "平均单价(元/㎡)", "成交套数", "成交金额(万元)", "环比涨幅(%)", "供应套数"],
    ["浦东新区", "65000", "1200", "93600", "5.2", "800"],
    ["徐汇区", "78000", "800", "74880", "3.8", "600"],
    ["静安区", "85000", "600", "61200", "4.1", "400"]
  ]
}
```

**智能分组分析**：
1. **量级差异分析**：
   - 平均单价：65000-85000元/㎡（万元级）
   - 成交套数：600-1200套（千级）
   - 成交金额：61200-93600万元（万级）
   - 环比涨幅：3.8-5.2%（个位数级）
   - 供应套数：400-800套（千级）

2. **分组策略制定**：
   - **价格金额组**：平均单价(6.5-8.5万元/㎡)、成交金额(612-936万元) - 量级兼容
   - **数量对比组**：成交套数(600-1200套)、供应套数(400-800套) - 量级一致
   - **涨幅分布组**：环比涨幅(3.8-5.2%) - 独立展示，适合PIE图

3. **避免的错误分组**：
   - ❌ 单价+套数：量级差异65000:1200 ≈ 54:1（接近阈值，但语义不关联）
   - ❌ 金额+套数：量级差异93600:1200 = 78:1（接近阈值，应分开）

**拆分转换结果**：
```json
[
  {
    "serial": "2.1.1",
    "type": "CHART",
    "style": "BAR",
    "title": "各区域房价与成交金额对比（万元）",
    "cols": ["平均单价(万元/㎡)", "成交金额(万元)"],
    "content": [
      {"title": "浦东新区", "content": [6.5, 936]},
      {"title": "徐汇区", "content": [7.8, 749]},
      {"title": "静安区", "content": [8.5, 612]}
    ]
  },
  {
    "serial": "2.1.2",
    "type": "CHART",
    "style": "BAR",
    "title": "各区域成交与供应对比（套）",
    "cols": ["成交套数", "供应套数"],
    "content": [
      {"title": "浦东新区", "content": [1200, 800]},
      {"title": "徐汇区", "content": [800, 600]},
      {"title": "静安区", "content": [600, 400]}
    ]
  },
  {
    "serial": "2.1.3",
    "type": "CHART",
    "style": "PIE",
    "title": "各区域价格涨幅分布（%）",
    "content": [
      {"title": "浦东新区", "content": 5.2},
      {"title": "徐汇区", "content": 3.8},
      {"title": "静安区", "content": 4.1}
    ]
  }
]
```

**处理要点说明**：
1. **数据无重复**：每个原始数据点只在一个图表中出现
2. **量级协调**：同一图表内数据量级差异控制在合理范围内（<10:1）
3. **主题明确**：每个图表都有清晰的展示主题和目的
4. **单位标注**：图表标题中明确标注数据单位，数值保持纯数字格式
5. **类型匹配**：根据数据特征选择最适合的图表类型
6. **层级保持**：拆分图表保持原有的serial层级结构

## 图表类型选择指南

### PIE图适用场景
- 面积分布、区域分布、价格段分布
- 百分比数据、占比数据
- 分类数据的构成分析

### BAR图适用场景
- 月度对比、多系列对比、分类数据对比
- 不同项目间的数值比较
- 数据不连续或稀疏的时间序列

### LINE图适用场景
- 价格走势、成交趋势、时间序列变化
- 连续性良好的数据系列
- 趋势分析和预测展示

**LINE图结构要求**：
- cols字段必须为x轴标线（时间点、月份等）
- content.title字段必须为数据分类名称（指标名称等）

## 最终质量验证

### 忠实性验证（最高优先级）
**数据来源追溯**：
- 验证每个CHART控件的数据都来源于对应的TABLE控件
- 确认没有添加、修改或推测任何数据
- 检查数值转换的准确性

**内容完整性检查**：
- 确认原始markdown的所有重要内容都有对应控件承载
- 验证分析性内容是否完整保留
- 检查是否遗漏任何有价值的信息

### 结构完整性验证
**基础结构检查**：
- 验证DocumentData的基本结构完整
- 检查type、title、subtitle字段设置
- 确认widgets数组结构正确

**控件格式验证**：
- 检查所有控件包含必需的serial和type字段
- 验证serial编号符合层级规则
- 确认各控件的字段格式符合规范

### 数据准确性验证
**数值类型检查**：
- 验证CHART控件中所有数值为纯数字类型
- 检查万单位转换的准确性
- 确认同一图表内数值单位一致

**格式规范检查**：
- 验证JSON字符串转义正确
- 确认LIST控件使用对象数组格式
- 检查TABLE和CHART控件的格式规范

## 输入数据格式
接收来自Step 4的结构化数据：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "widgets": [ /* 已生成的所有控件 */ ],
  "remaining_segments": [ /* CHART候选片段 */ ],
  "processing_metadata": {
    "step": 4,
    "chart_candidates": [ /* 图表候选信息 */ ]
  }
}
```

## 输出格式要求

输出最终的DocumentData JSON结构，移除processing_metadata：

```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 经过完整处理和验证的控件数组
  ]
}
```

## 处理流程

### 1. 输入验证
- 验证Step 4输出的数据结构完整性
- 检查chart_candidates中的转换指导信息
- 确认TABLE控件的数据格式

### 2. 图表转换决策
- 基于置信度和风险因素做出转换决策
- 应用数据连续性和量级差异处理规则
- 执行必要的表格拆分和数据分组

### 3. CHART控件生成
- 为转换决策通过的TABLE控件生成CHART控件
- 应用万单位转换规则
- 确保数值类型和格式正确

### 4. 最终质量验证
- 执行忠实性验证，确保数据来源可追溯
- 进行结构完整性和格式规范检查
- 验证数据准确性和类型正确性

### 5. 输出优化
- 使用预分配的序列编号，确保编号连续性
- 确保文档结构清晰合理
- 移除处理元数据，输出最终JSON

**序列编号应用要求**：
- **使用预分配编号**：直接使用Step2提供的serial_mapping中的编号
- **无需重新计算**：编号已预分配，确保全局连续性
- **拆分图表编号**：如需拆分TABLE为多个CHART，使用子级编号（如"2.1.1"、"2.1.2"）

## 核心执行要求

1. **转换决策**：基于Step 4的评估结果做出合理的图表转换决策
2. **智能处理**：应用数据连续性、量级差异等智能处理规则
3. **数值转换**：正确应用万单位转换，确保单位一致性
4. **忠实性验证**：确保所有数据都来源于原始内容，无虚构信息
5. **格式规范**：确保所有控件格式严格符合规范要求
6. **质量保证**：进行全面的质量检查，输出高质量的最终结果

<----------------------------(user_prompt)---------------------------->

请基于Step 4的结构化数据，处理图表转换并进行最终验证，输出符合规范的最终DocumentData JSON。

### 输入数据
```json
${step4_output}
```

### 处理要求

1. **转换决策**：基于Step 4的评估结果做出合理的图表转换决策
2. **智能处理**：应用数据连续性、量级差异等智能处理规则
3. **数值转换**：正确应用万单位转换，确保单位一致性
4. **忠实性验证**：确保所有数据都来源于原始内容，无虚构信息
5. **格式规范**：确保所有控件格式严格符合规范要求
6. **质量保证**：进行全面的质量检查，输出高质量的最终结果

请开始处理，输出最终的DocumentData JSON结构（不包含processing_metadata）。
<----------------------------(step4_output)---------------------------->

{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"subtitle": "",
"widgets": [
{
"serial": "0",
"type": "TITLE",
"style": "DOCUMENT",
"title": "慧芝湖花园3室2厅2卫价值评测报告"
},
{
"serial": "1",
"type": "TITLE",
"style": "SECTION",
"title": "小区基本信息分析"
},
{
"serial": "1.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "1. 小区户型分析"
},
{
"serial": "1.1.1",
"type": "TEXT",
"style": "BOARD",
"title": "户型评估",
"content": "**户型评估**：小区在售房源以3室户型为主，占比**42.86%**，挂牌均价最高达106,985元/㎡；2室户型价格相对较低，为100,000元/㎡。"
},
{
"serial": "1.2",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "2. 板块市场对比分析"
},
{
"serial": "2",
"type": "TITLE",
"style": "SECTION",
"title": "区域价值"
},
{
"serial": "2.1",
"type": "TEXT",
"style": "BOARD",
"content": "作为上海市静安区的核心居住板块，慧芝湖花园（三期）坐拥大宁国际商圈优质资源，45%高绿化率营造出都市绿洲般的居住环境。项目由嘉华(中国)投资有限公司开发，龙湖物业提供专业管理（物业费2.7元/月/㎡），完美融合国际化社区品质与便利生活体验。"
},
{
"serial": "3",
"type": "TITLE",
"style": "SECTION",
"title": "生活配套"
},
{
"serial": "4",
"type": "TITLE",
"style": "SECTION",
"title": "教育资源"
},
{
"serial": "4.1",
"type": "TEXT",
"style": "BOARD",
"title": "特色优势",
"content": "形成500米优质教育圈，实现\"出家门即校门\"的便利教育体验，尤其适合年轻家庭置业需求。"
},
{
"serial": "5",
"type": "TITLE",
"style": "SECTION",
"title": "小区品质"
},
{
"serial": "5.1",
"type": "TEXT",
"style": "BOARD",
"title": "生态美学",
"content": "中央景观带与组团绿化相结合，45%绿化率打造花园式社区，建筑间距达行业高标准"
},
{
"serial": "5.2",
"type": "TEXT",
"style": "BOARD",
"title": "服务标准",
"content": "龙湖物业提供星级服务，配备3494个停车位（车位比1:0.7），实行人车分流管理"
},
{
"serial": "5.3",
"type": "TEXT",
"style": "BOARD",
"title": "生活场景",
"content": "晨间可步行至星巴克享用早餐，下午在社区园林散步，晚间步行5分钟即达购物中心，完美演绎静安高品质生活范式"
},
{
"serial": "6",
"type": "TITLE",
"style": "SECTION",
"title": "专业服务推荐"
},
{
"serial": "0.1",
"type": "LIST",
"style": "BOARD",
"title": "报告基本信息",
"content": [
{
"title": "数据来源",
"content": "上海市房地产交易平台"
},
{
"title": "评测时间",
"content": "2025年7月"
},
{
"title": "平均价格概览",
"content": "**97,600元/㎡**"
}
]
},
{
"serial": "1.1.4",
"type": "LIST",
"style": "BOARD",
"title": "趋势分析",
"content": [
{
"title": "",
"content": "挂牌均价呈现波动上升趋势，从2024年10月的100,000元/㎡升至2025年7月的105,689元/㎡"
},
{
"title": "",
"content": "2025年3月达到挂牌均价峰值109,001元/㎡"
},
{
"title": "",
"content": "成交活跃期集中在2024年11-12月，最高单月成交6套"
}
]
},
{
"serial": "1.2.2",
"type": "LIST",
"style": "BOARD",
"title": "板块对比分析",
"content": [
{
"title": "",
"content": "小区挂牌均价(105,689元/㎡)显著高于板块平均水平(76,071元/㎡)，溢价约39%"
},
{
"title": "",
"content": "小区成交均价波动较大，2025年2月出现异常低值73,902元/㎡"
},
{
"title": "",
"content": "板块成交高峰出现在2024年12月，单月成交72套"
}
]
},
{
"serial": "2.2",
"type": "LIST",
"style": "BOARD",
"title": "区域核心价值体现于",
"content": [
{
"title": "双轨交枢纽优势",
"content": "步行范围内覆盖1号线马戏城站与多条公交干线"
},
{
"title": "全龄教育资源矩阵",
"content": "1公里内覆盖幼儿园至小学优质教育机构"
},
{
"title": "商业配套集群",
"content": "百联莘荟购物中心等商业体形成5分钟生活圈"
},
{
"title": "生态宜居品质",
"content": "2.5低容积率与板楼设计保障居住舒适度"
}
]
},
{
"serial": "2.3",
"type": "LIST",
"style": "BOARD",
"title": "交通网络",
"content": [
{
"title": "轨交动脉",
"content": "距1号线马戏城站约430米（步行6分钟），快速连接人民广场、徐家汇等核心商圈"
},
{
"title": "公交覆盖",
"content": "广中路平型关路站（305米）汇集107/547/767等8条公交线路，形成辐射全城的交通网络"
},
{
"title": "路网体系",
"content": "平型关路、广中路、共和新路构成三横三纵路网，15分钟车程直达内环高架"
}
]
},
{
"serial": "3.1",
"type": "LIST",
"style": "BULLET",
"title": "医疗旗舰",
"content": [
{
"title": "",
"content": "登特口腔（348米）：专业口腔医疗机构"
},
{
"title": "",
"content": "益丰大药房（166米）：24小时便民药房"
},
{
"title": "",
"content": "赞瞳眼科诊所（500米）：专科眼科服务"
}
]
},
{
"serial": "3.2",
"type": "LIST",
"style": "BULLET",
"title": "商业矩阵",
"content": [
{
"title": "",
"content": "百联莘荟购物中心（500米）：**4.5星评级综合体**，内含盒马奥莱、Tims咖啡等品牌"
},
{
"title": "",
"content": "宝华现代城商业街（489米）：特色餐饮聚集地"
},
{
"title": "",
"content": "百果园（56米）：社区生鲜便利站"
}
]
},
{
"serial": "3.3",
"type": "LIST",
"style": "BULLET",
"title": "休闲图鉴",
"content": [
{
"title": "",
"content": "自然运动·普拉提（433米）：高端健身会所"
},
{
"title": "",
"content": "星巴克（199米）：社区咖啡社交空间"
},
{
"title": "",
"content": "和记小菜（308米）：4.6分评价的本帮菜餐厅"
}
]
},
{
"serial": "4.2",
"type": "LIST",
"style": "BULLET",
"title": "全龄教育链",
"content": [
{
"title": "",
"content": "大宁国际第二幼儿园（355米）：区级示范园"
},
{
"title": "",
"content": "上海市大宁国际小学（254米）：优质公办教育"
},
{
"title": "",
"content": "静安区大宁路小学（518米）：**历史悠久的重点小学**"
}
]
},
{
"serial": "5.4",
"type": "LIST",
"style": "BULLET",
"title": "建筑基因",
"content": [
{
"title": "",
"content": "纯板楼设计（2004-2009年建成）"
},
{
"title": "",
"content": "主力户型72-174㎡（1-4房）"
},
{
"title": "",
"content": "2.5容积率保障低密度居住体验"
}
]
},
{
"serial": "0.2",
"type": "TABLE",
"style": "BOARD",
"title": "评测房源基本信息",
"cols": ["项目", "详情"],
"content": [
[
{"type": "TEXT", "content": "城市"},
{"type": "TEXT", "content": "上海市"}
],
[
{"type": "TEXT", "content": "小区名称"},
{"type": "TEXT", "content": "慧芝湖花园"}
],
[
{"type": "TEXT", "content": "户型"},
{"type": "TEXT", "content": "3室2厅2卫"}
],
[
{"type": "TEXT", "content": "建筑面积"},
{"type": "TEXT", "content": "110㎡"}
],
[
{"type": "TEXT", "content": "朝向"},
{"type": "TEXT", "content": "朝南"}
],
[
{"type": "TEXT", "content": "预估单价"},
{"type": "TEXT", "content": "97,600元/㎡"}
],
[
{"type": "TEXT", "content": "板块位置"},
{"type": "TEXT", "content": "凉城（挂牌板块：大宁板块）"}
]
]
},
{
"serial": "1.1.2",
"type": "TABLE",
"style": "NORMAL",
"title": "在售房源户型占比",
"cols": ["户型", "新增挂牌套数(套)", "挂牌均价(元/㎡)", "新增挂牌面积(㎡)"],
"content": [
[
{"type": "TEXT", "content": "2室"},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "100,000"},
{"type": "TEXT", "content": "196"}
],
[
{"type": "TEXT", "content": "3室"},
{"type": "TEXT", "content": "3"},
{"type": "TEXT", "content": "106,985", "recommended": true},
{"type": "TEXT", "content": "398", "recommended": true}
],
[
{"type": "TEXT", "content": "4室"},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "103,667"},
{"type": "TEXT", "content": "300"}
]
]
},
{
"serial": "1.1.3",
"type": "TABLE",
"style": "NORMAL",
"title": "小区近12个月市场走势",
"cols": ["月度", "挂牌均价(元/㎡)", "挂牌均价环比(%)", "新增挂牌套数(套)", "新增挂牌面积(㎡)", "成交套数(套)", "成交面积(㎡)", "成交均价(元/㎡)", "成交均价环比(%)"],
"content": [
[
{"type": "TEXT", "content": "2024年08月"},
{"type": "TEXT", "content": "-"},
{"type": "TEXT", "content": "0.00"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "1"},
{"type": "TEXT", "content": "34"},
{"type": "TEXT", "content": "17,059"},
{"type": "TEXT", "content": "-84.58"}
],
[
{"type": "TEXT", "content": "2024年09月"},
{"type": "TEXT", "content": "-"},
{"type": "TEXT", "content": "0.00"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "-"},
{"type": "TEXT", "content": "0.00"}
],
[
{"type": "TEXT", "content": "2024年10月"},
{"type": "TEXT", "content": "100,000"},
{"type": "TEXT", "content": "0.00"},
{"type": "TEXT", "content": "3"},
{"type": "TEXT", "content": "417"},
{"type": "TEXT", "content": "1"},
{"type": "TEXT", "content": "87"},
{"type": "TEXT", "content": "96,437"},
{"type": "TEXT", "content": "0.00"}
],
[
{"type": "TEXT", "content": "2024年11月"},
{"type": "TEXT", "content": "106,473"},
{"type": "TEXT", "content": "6.47"},
{"type": "TEXT", "content": "5"},
{"type": "TEXT", "content": "482"},
{"type": "TEXT", "content": "3"},
{"type": "TEXT", "content": "357"},
{"type": "TEXT", "content": "91,120"},
{"type": "TEXT", "content": "-5.51"}
],
[
{"type": "TEXT", "content": "2024年12月"},
{"type": "TEXT", "content": "105,950"},
{"type": "TEXT", "content": "-0.49"},
{"type": "TEXT", "content": "7"},
{"type": "TEXT", "content": "763"},
{"type": "TEXT", "content": "6"},
{"type": "TEXT", "content": "556"},
{"type": "TEXT", "content": "91,973"},
{"type": "TEXT", "content": "0.94"}
],
[
{"type": "TEXT", "content": "2025年01月"},
{"type": "TEXT", "content": "102,416"},
{"type": "TEXT", "content": "-3.34"},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "178"},
{"type": "TEXT", "content": "1"},
{"type": "TEXT", "content": "88"},
{"type": "TEXT", "content": "96,591"},
{"type": "TEXT", "content": "5.02"}
],
[
{"type": "TEXT", "content": "2025年02月"},
{"type": "TEXT", "content": "101,960"},
{"type": "TEXT", "content": "-0.45"},
{"type": "TEXT", "content": "7"},
{"type": "TEXT", "content": "903"},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "123"},
{"type": "TEXT", "content": "73,902"},
{"type": "TEXT", "content": "-23.49"}
],
[
{"type": "TEXT", "content": "2025年03月"},
{"type": "TEXT", "content": "109,001", "recommended": true},
{"type": "TEXT", "content": "6.91"},
{"type": "TEXT", "content": "10"},
{"type": "TEXT", "content": "1,201", "recommended": true},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "296"},
{"type": "TEXT", "content": "93,176"},
{"type": "TEXT", "content": "26.08"}
],
[
{"type": "TEXT", "content": "2025年04月"},
{"type": "TEXT", "content": "108,324"},
{"type": "TEXT", "content": "-0.62"},
{"type": "TEXT", "content": "2"},
{"type": "TEXT", "content": "179"},
{"type": "TEXT", "content": "1"},
{"type": "TEXT", "content": "73"},
{"type": "TEXT", "content": "94,247"},
{"type": "TEXT", "content": "1.15"}
],
[
{"type": "TEXT", "content": "2025年05月"},
{"type": "TEXT", "content": "107,222"},
{"type": "TEXT", "content": "-1.02"},
{"type": "TEXT", "content": "4"},
{"type": "TEXT", "content": "468"},
{"type": "TEXT", "content": "3"},
{"type": "TEXT", "content": "238"},
{"type": "TEXT", "content": "85,882"},
{"type": "TEXT", "content": "-8.88"}
],
[
{"type": "TEXT", "content": "2025年06月"},
{"type": "TEXT", "content": "103,070"},
{"type": "TEXT", "content": "-3.87"},
{"type": "TEXT", "content": "6"},
{"type": "TEXT", "content": "645"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "-"},
{"type": "TEXT", "content": "0.00"}
],
[
{"type": "TEXT", "content": "2025年07月"},
{"type": "TEXT", "content": "105,689"},
{"type": "TEXT", "content": "0.00"},
{"type": "TEXT", "content": "4"},
{"type": "TEXT", "content": "559"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "0"},
{"type": "TEXT", "content": "-"},
{"type": "TEXT", "content": "0.00"}
]
]
},
{
"serial": "1.2.1",
"type": "TABLE",
"style": "NORMAL",
"title": "板块近12个月走势",
"cols": ["月度", "挂牌均价(元/㎡)", "挂牌均价环比(%)", "新增挂牌套数(套)", "新增挂牌面积(㎡)", "成交套数(套)", "成交面积(㎡)", "成交均价(元/㎡)", "成交均价环比(%)"],
"content": [
[
{"type": "TEXT", "content": "2024年08月"},
{"type": "TEXT", "content": "78,913"},
{"type": "TEXT", "content": "-0.22"},
{"type": "TEXT", "content": "153"},
{"type": "TEXT", "content": "12,084"},
{"type": "TEXT", "content": "28"},
{"type": "TEXT", "content": "1,978"},
{"type": "TEXT", "content": "72,456"},
{"type": "TEXT", "content": "-12.09"}
],
[
{"type": "TEXT", "content": "2024年09月"},
{"type": "TEXT", "content": "82,594"},
{"type": "TEXT", "content": "4.66"},
{"type": "TEXT", "content": "173"},
{"type": "TEXT", "content": "14,040"},
{"type": "TEXT", "content": "31"},
{"type": "TEXT", "content": "2,305"},
{"type": "TEXT", "content": "76,633"},
{"type": "TEXT", "content": "5.76"}
],
[
{"type": "TEXT", "content": "2024年10月"},
{"type": "TEXT", "content": "82,346"},
{"type": "TEXT", "content": "-0.30"},
{"type": "TEXT", "content": "203"},
{"type": "TEXT", "content": "17,548"},
{"type": "TEXT", "content": "47"},
{"type": "TEXT", "content": "3,519"},
{"type": "TEXT", "content": "77,774"},
{"type": "TEXT", "content": "1.49"}
],
[
{"type": "TEXT", "content": "2024年11月"},
{"type": "TEXT", "content": "82,061"},
{"type": "TEXT", "content": "-0.35"},
{"type": "TEXT", "content": "191"},
{"type": "TEXT", "content": "16,101"},
{"type": "TEXT", "content": "63"},
{"type": "TEXT", "content": "4,917"},
{"type": "TEXT", "content": "79,483"},
{"type": "TEXT", "content": "2.20"}
],
[
{"type": "TEXT", "content": "2024年12月"},
{"type": "TEXT", "content": "80,577"},
{"type": "TEXT", "content": "-1.81"},
{"type": "TEXT", "content": "175"},
{"type": "TEXT", "content": "13,939"},
{"type": "TEXT", "content": "72"},
{"type": "TEXT", "content": "5,804"},
{"type": "TEXT", "content": "81,676"},
{"type": "TEXT", "content": "2.76"}
],
[
{"type": "TEXT", "content": "2025年01月"},
{"type": "TEXT", "content": "77,387"},
{"type": "TEXT", "content": "-3.96"},
{"type": "TEXT", "content": "90"},
{"type": "TEXT", "content": "7,322"},
{"type": "TEXT", "content": "34"},
{"type": "TEXT", "content": "2,889"},
{"type": "TEXT", "content": "79,855"},
{"type": "TEXT", "content": "-2.23"}
],
[
{"type": "TEXT", "content": "2025年02月"},
{"type": "TEXT", "content": "80,282"},
{"type": "TEXT", "content": "3.74"},
{"type": "TEXT", "content": "217"},
{"type": "TEXT", "content": "18,538"},
{"type": "TEXT", "content": "22"},
{"type": "TEXT", "content": "1,402"},
{"type": "TEXT", "content": "69,882"},
{"type": "TEXT", "content": "-12.49"}
],
[
{"type": "TEXT", "content": "2025年03月"},
{"type": "TEXT", "content": "81,956"},
{"type": "TEXT", "content": "2.09"},
{"type": "TEXT", "content": "226"},
{"type": "TEXT", "content": "19,118"},
{"type": "TEXT", "content": "82"},
{"type": "TEXT", "content": "6,573"},
{"type": "TEXT", "content": "74,976"},
{"type": "TEXT", "content": "7.29"}
],
[
{"type": "TEXT", "content": "2025年04月"},
{"type": "TEXT", "content": "78,560"},
{"type": "TEXT", "content": "-4.14"},
{"type": "TEXT", "content": "173"},
{"type": "TEXT", "content": "14,109"},
{"type": "TEXT", "content": "49"},
{"type": "TEXT", "content": "3,349"},
{"type": "TEXT", "content": "69,449"},
{"type": "TEXT", "content": "-7.37"}
],
[
{"type": "TEXT", "content": "2025年05月"},
{"type": "TEXT", "content": "79,206"},
{"type": "TEXT", "content": "0.82"},
{"type": "TEXT", "content": "190"},
{"type": "TEXT", "content": "15,946"},
{"type": "TEXT", "content": "50"},
{"type": "TEXT", "content": "3,688"},
{"type": "TEXT", "content": "71,457"},
{"type": "TEXT", "content": "2.89"}
],
[
{"type": "TEXT", "content": "2025年06月"},
{"type": "TEXT", "content": "78,951"},
{"type": "TEXT", "content": "-0.32"},
{"type": "TEXT", "content": "172"},
{"type": "TEXT", "content": "15,655"},
{"type": "TEXT", "content": "30"},
{"type": "TEXT", "content": "2,369"},
{"type": "TEXT", "content": "74,596"},
{"type": "TEXT", "content": "4.39"}
],
[
{"type": "TEXT", "content": "2025年07月"},
{"type": "TEXT", "content": "76,071"},
{"type": "TEXT", "content": "0.00"},
{"type": "TEXT", "content": "108"},
{"type": "TEXT", "content": "10,025"},
{"type": "TEXT", "content": "4"},
{"type": "TEXT", "content": "356"},
{"type": "TEXT", "content": "60,253"},
{"type": "TEXT", "content": "0.00"}
]
]
}
],
"remaining_segments": [
{
"segment_id": "seg_029",
"original_content": "### 置业顾问推荐\n\n**张经理** - 资深置业顾问\n- 联系电话：138-0000-1234\n- 服务年限：8年\n- 专业领域：静安区房产投资、学区房置业\n- 服务区域：大宁板块、凉城板块\n- 客户评价：4.8分（基于126条评价）\n- 成功案例：累计成交房源超过200套，专注中高端住宅",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "BROKER",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "经纪人信息卡片，包含姓名、联系方式、专业领域等关键字段"
},
"preassigned_serial": "6.1"
},
{
"segment_id": "seg_030",
"original_content": "### 优质房源推荐\n\n**慧芝湖花园精选房源**\n- 户型：3室2厅2卫\n- 建筑面积：110㎡\n- 楼层：15/18层\n- 朝向：朝南\n- 装修状况：精装修\n- 总价：1073万\n- 单价：97,545元/㎡\n- 房产类型：商品房\n- 特色标签：学区房、地铁房、满五唯一",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "HOUSING",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "房源信息卡片，包含户型、面积、价格等关键字段"
},
"preassigned_serial": "6.2"
},
{
"segment_id": "seg_031",
"original_content": "### 社区配套介绍\n\n**慧芝湖花园社区信息**\n- 建筑年代：2004-2009年\n- 物业公司：龙湖物业\n- 物业费：2.7元/月/㎡\n- 绿化率：45%\n- 容积率：2.5\n- 停车位信息：3494个车位，车位比1:0.7\n- 主要配套设施：中央景观带、健身会所、24小时安保、人车分流系统",
"content_type": "card",
"recommended_widget": {
"primary_type": "CARD",
"primary_style": "COMMUNITY",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "小区信息卡片，包含建筑年代、物业、绿化率等关键字段"
},
"preassigned_serial": "6.3"
}
],
"processing_metadata": {
"step": 4,
"widgets_generated": {
"TITLE": 10,
"TEXT": 6,
"LIST": 10,
"TABLE": 4
},
"chart_candidates": [
{
"segment_id": "seg_006",
"table_serial": "1.1.2",
"recommended_chart_type": "BAR",
"conversion_confidence": 0.9,
"risk_factors": []
},
{
"segment_id": "seg_008",
"table_serial": "1.1.3",
"recommended_chart_type": "LINE",
"conversion_confidence": 0.85,
"risk_factors": ["数据缺失"]
},
{
"segment_id": "seg_011",
"table_serial": "1.2.1",
"recommended_chart_type": "LINE",
"conversion_confidence": 0.85,
"risk_factors": ["数据缺失"]
}
],
"type_adjustments": [],
"title_duplications_resolved": 0,
"remaining_types": [
"CARD"
],
"processing_notes": "表格控件处理完成，所有TABLE类型内容已处理完成，图表候选已评估，剩余CARD类型内容已完整保留到remaining_segments。序列编号严格保持连续性，章节编号按顺序递增。"
}
}
