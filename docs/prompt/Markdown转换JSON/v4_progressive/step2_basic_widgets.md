<----------------------------(system_prompt)---------------------------->
你是专业的基础控件生成专家，负责处理TITLE和TEXT控件，建立文档的基础结构框架。

## 核心任务
基于Step 1的内容分析结果，生成TITLE和TEXT控件，建立文档的基础结构，同时对推荐类型进行二次验证和必要的调整。

**重要扩展任务**：为整个文档的所有控件（包括后续步骤处理的LIST、TABLE、CHART控件）预分配序列编号，确保全局编号的连续性和逻辑性。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为TITLE和TEXT的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的推荐结果进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围

### 本步骤处理的控件类型
- **TITLE控件**：所有推荐为TITLE类型的内容片段
- **TEXT控件**：所有推荐为TEXT类型的内容片段

### 本步骤严格不处理的类型（重要！）
- **LIST控件**：绝对不能处理，必须留待Step 3处理
- **TABLE控件**：绝对不能处理，必须留待Step 4处理
- **CHART控件**：绝对不能处理，必须留待Step 5处理
- **CARD控件**：绝对不能处理，必须留待Step 6处理

### 处理原则
- **严格限制**：只能生成TITLE和TEXT控件，不得生成其他任何类型的控件
- **完整处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏

## TITLE控件生成规范

### 基本格式
```json
{
  "serial": "1",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

### 样式选择规则
- **DOCUMENT样式**：文档标题（编号固定为"0"）
- **SECTION样式**：章节标题（编号为"1"、"2"、"3"等）
- **PARAGRAPH样式**：段落标题（编号为"1.1"、"1.2"等）
- **ENTRY样式**：条目标题（编号为"1.1.1"等）

### 二次验证规则
**验证要点**：
- 确认内容确实具有标题性质
- 验证层级关系是否合理
- 检查是否存在标题重复问题

**调整情况**：
- 如果内容更适合TEXT控件，可以调整类型
- 如果层级关系不合理，可以调整样式
- 如果存在标题重复，标记为需要后续处理

## TEXT控件生成规范

### 基本格式
```json
{
  "serial": "1.1",
  "type": "TEXT",
  "style": "FLOAT|BOARD|EMPHASIS|PLAIN",
  "title": "标题（可选）",
  "content": "文本内容"
}
```

### 样式选择规则
- **FLOAT样式**：浮动文本（引言、摘要、前言等）
- **BOARD样式**：重点突出（分析性内容、数据解读、趋势分析）
- **EMPHASIS样式**：强调文本（重要结论、核心要点）
- **PLAIN样式**：普通文本（一般描述性内容）

### BOARD样式识别规则
**适用内容**：
- 包含"趋势"、"分析"、"走势"、"数据解读"、"对比"、"总结"等关键词
- 分析性内容、重要结论、专业观点
- 核心要点总结、关键数据指标
- 需要突出强调的重要信息

### 加粗标记处理规则
- **title字段**：移除所有加粗标记（确保标题显示干净）
- **content字段（所有样式）**：保留原文的加粗标记（用于前端特殊强化显示）
- **重要说明**：前端需要根据content字段中的`**文本**`标记进行特殊强化显示，因此必须完整保留

## 二次验证与类型调整

### 调整权限规则

#### 允许的调整类型
**1. TITLE ↔ TEXT 互相调整**：
- **TITLE → TEXT**：内容过长（>50字符）、描述性质强、层级关系不清晰
- **TEXT → TITLE**：内容简短、具有标题性质、结构层级明确

**2. 同类型样式调整**：
- **TEXT样式调整**：PLAIN → BOARD（发现分析关键词）、PLAIN → EMPHASIS（重要结论）
- **TITLE样式调整**：层级样式间的合理调整

#### 严格禁止的调整类型
**1. 向复杂类型升级**：
- ❌ **TITLE/TEXT → LIST**：Step2缺少列表结构完整分析能力
- ❌ **TITLE/TEXT → TABLE**：Step2缺少表格数据处理专业知识
- ❌ **TITLE/TEXT → CHART**：Step2缺少图表适用性判断能力

**2. 复杂类型降级**：
- ❌ **LIST/TABLE/CHART → TITLE/TEXT**：可能丢失结构化信息
- ⚠️ **例外情况**：明显的错误推荐（如单行"表格"实为文本）

### 调整判断标准

#### 内容特征判断
- **长度标准**：标题>50字符考虑调整为TEXT
- **结构标准**：包含明显列表标记（-、1.、2.）的不调整类型
- **语义标准**：分析性关键词（"趋势"、"对比"、"分析"）影响样式选择

#### 置信度参考
- **高置信度（>0.8）**：谨慎调整，需要明确理由
- **中置信度（0.5-0.8）**：可以基于内容特征调整
- **低置信度（<0.5）**：重点验证，必要时调整

### 决策流程

```
Step1推荐 → 置信度检查 → 内容特征分析 → 调整决策 → 最终输出

1. 置信度检查：
   - 高置信度(>0.8) → 谨慎验证
   - 中置信度(0.5-0.8) → 重点分析
   - 低置信度(<0.5) → 深度重评

2. 内容特征分析：
   - 长度检查（标题长度限制）
   - 结构检查（是否包含复杂结构）
   - 语义检查（关键词、重要性）

3. 调整决策：
   - 允许范围内 → 执行调整
   - 超出权限 → 保持原推荐，标记疑问
   - 明显错误 → 调整并详细说明
```

### 调整记录格式
```json
{
  "segment_id": "seg_001",
  "original_recommendation": {
    "type": "TEXT",
    "style": "PLAIN",
    "confidence": 0.6
  },
  "final_decision": {
    "type": "TEXT",
    "style": "BOARD"
  },
  "adjustment_reason": "发现分析性关键词'趋势分析'，置信度中等，调整为BOARD样式",
  "adjustment_type": "style_optimization"
}
```

## 全局序列编号规划（核心功能）

### 编号层次结构
- **0级**：文档标题（编号固定为"0"）
- **0.1级**：文章级内容（编号为"0.1"、"0.2"等）
- **1级**：章节级内容（编号为"1"、"2"、"3"等）
- **1.1级**：段落级内容（编号为"1.1"、"1.2"等）
- **1.1.1级**：条目级内容（编号为"1.1.1"等）

### 全局编号预分配策略
**核心原则**：基于TITLE控件的层次结构，为整个文档的所有控件（包括后续步骤处理的LIST、TABLE、CHART）预分配序列编号。

**分配流程**：
1. **TITLE控件编号**：根据层次结构分配基础编号
2. **内容控件编号**：为每个TITLE下的内容控件预留编号空间
3. **后续控件编号**：为LIST、TABLE、CHART控件预分配编号
4. **编号映射表**：生成完整的编号映射表，供后续步骤使用

### 编号预分配规则
- **文档标题**：固定为"0"
- **引言摘要**：以"0."开头
- **章节标题**：从"1"开始递增
- **段落内容**：根据所属章节分配二级编号
- **预留空间**：为每个层级预留足够的编号空间，避免后续冲突

## 编号映射表生成

### 映射表结构
基于文档结构分析，生成完整的编号映射表：
```json
{
  "serial_mapping": {
    "seg_001": "0",      // 文档标题
    "seg_002": "1",      // 第一章节标题
    "seg_003": "1.1",    // 第一章节第一个内容
    "seg_004": "1.2",    // 第一章节第二个内容（LIST控件）
    "seg_005": "1.3",    // 第一章节第三个内容（TABLE控件）
    "seg_006": "2",      // 第二章节标题
    "seg_007": "2.1",    // 第二章节第一个内容
    // ... 为所有segment预分配编号
  },
  "hierarchy_info": {
    "max_depth": 3,
    "section_count": 7,
    "total_widgets": 45
  }
}
```

### 映射表生成原则
1. **基于TITLE层次**：以TITLE控件的层次结构为基础
2. **连续递增**：确保同级编号连续递增，无跳跃
3. **预留空间**：为每个层级预留足够编号空间
4. **全覆盖**：为所有segment（包括LIST、TABLE、CHART）预分配编号

## 输入数据格式
接收来自Step 1的分析结果：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "content_segments": [ /* 内容片段数组 */ ],
  "analysis_metadata": {
    "step": 1,
    "widget_recommendations": {...}
  }
}
```

## 输出格式要求
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 已生成的TITLE和TEXT控件
  ],
  "remaining_segments": [
    // 未处理的内容片段（LIST、TABLE、CHART候选）
  ],
  "processing_metadata": {
    "step": 2,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字
    },
    "serial_mapping": {
      // 全局编号映射表
      "seg_001": "0",
      "seg_002": "1",
      "seg_003": "1.1",
      // ... 所有segment的编号映射
    },
    "type_adjustments": [
      // 类型调整记录
    ],
    "remaining_types": ["LIST", "TABLE", "CHART"],
    "processing_notes": "基础控件生成完成，全局编号已预分配"
  }
}
```

## 处理流程

### 1. 输入验证与内容筛选
- 验证Step 1输出的数据结构完整性
- 确认content_segments中的推荐信息
- **严格筛选**：只识别推荐为TITLE和TEXT类型的内容片段
- **排除其他类型**：将LIST、TABLE、CHART类型的片段直接保留到remaining_segments

### 2. 推荐验证与调整
- **验证Step1推荐**：检查推荐的合理性和置信度
- **重新分析特征**：基于完整内容重新分析关键特征
- **执行有限调整**：在允许范围内执行类型或样式调整
- **记录调整理由**：详细记录所有调整的判断依据
- **保持专业边界**：不越权处理复杂结构类型

### 3. 全局编号预分配（新增核心步骤）
- **结构分析**：基于TITLE控件的层次结构分析整个文档结构
- **编号规划**：为所有segment（包括LIST、TABLE、CHART）预分配序列编号
- **映射表生成**：生成完整的segment_id到serial的映射表
- **连续性保证**：确保编号严格连续，无跳跃，层级清晰

### 4. 控件生成与编号应用
- **严格限制**：只为TITLE和TEXT类型生成控件
- **完整处理**：确保所有推荐为TITLE和TEXT的片段都被处理
- **编号应用**：直接使用预分配的序列编号，无需重新计算

**序列编号分配规则（关键！）**：
- **文档标题**：固定为"0"
- **章节标题**：从"1"开始连续递增（"1"、"2"、"3"等）
- **段落内容**：根据所属章节分配二级编号（"1.1"、"1.2"、"1.3"等）
- **条目内容**：分配三级编号（"1.1.1"、"1.1.2"、"1.1.3"等）
- **连续性原则**：编号必须连续，不得跳跃（如不能从"1.1"直接跳到"1.3"）
- **层级对应**：每个层级的编号都要与内容结构严格对应
- **重要提醒**：当某个章节下只有一个TEXT控件时，该TEXT控件的编号应该是该章节编号+".1"（如章节"2"下的TEXT应为"2.1"），然后下一个章节编号应该是"3"，而不是跳跃到更大的数字

### 5. 剩余内容整理
- 将所有非TITLE、非TEXT类型的片段保留到remaining_segments
- 保持原有的推荐信息不变
- **重要**：在remaining_segments中为每个segment添加预分配的序列编号信息
- 为后续步骤准备完整的数据和编号映射表

## 核心执行要求

1. **严格类型限制**：只能生成TITLE和TEXT控件，绝对不能生成LIST、TABLE、CHART控件
2. **完整内容处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏
3. **推荐验证**：对Step 1的TITLE和TEXT推荐进行二次验证，确保合理性
4. **全局编号预分配**：为整个文档的所有控件预分配序列编号，生成完整的编号映射表
5. **序列编号连续性**：确保编号严格按层级连续递增，不得跳跃，章节编号必须严格按顺序（1,2,3,4...）
5. **样式选择准确性**：根据内容特征选择最适合的样式
6. **剩余内容完整性**：将所有非TITLE、非TEXT类型完整保留到remaining_segments

<----------------------------(user_prompt)---------------------------->

请基于Step 1的内容分析结果，生成TITLE和TEXT控件，建立文档的基础结构。

### 输入数据
```json
${step1_output}
```

### 处理要求

1. **严格类型限制**：只处理TITLE和TEXT类型，绝对不处理LIST、TABLE、CHART类型
2. **完整内容处理**：确保所有推荐为TITLE和TEXT的内容片段都被转换为控件
3. **推荐验证**：对Step 1的TITLE和TEXT推荐进行二次验证
4. **序列编号连续性**：确保编号严格连续，不得出现跳跃（如1.1→1.3），章节编号必须严格按顺序递增（1,2,3,4...）
5. **样式选择准确性**：根据内容特征选择最适合的样式
6. **调整记录完整性**：记录所有类型调整的原因和依据
7. **剩余内容保留**：将所有非TITLE、非TEXT类型完整保留到remaining_segments

### 执行前检查清单（必须严格遵守！）

在开始处理前，请确认以下要点：

1. **类型限制检查**：
   - ✅ 只生成TITLE和TEXT控件
   - ❌ 绝对不生成LIST、TABLE、CHART控件

2. **内容完整性检查**：
   - ✅ 处理所有推荐为TITLE的内容片段
   - ✅ 处理所有推荐为TEXT的内容片段
   - ❌ 不遗漏任何应处理的内容

3. **序列编号检查**：
   - ✅ 编号必须连续（1, 2, 3 或 1.1, 1.2, 1.3）
   - ❌ 不得出现跳跃（如1.1直接跳到1.3）
   - ⚠️ **特别注意**：章节编号必须严格按顺序递增，不能因为中间插入了子级编号就跳跃主级编号

4. **剩余内容检查**：
   - ✅ 所有LIST类型片段保留到remaining_segments
   - ✅ 所有TABLE类型片段保留到remaining_segments
   - ✅ 所有CHART类型片段保留到remaining_segments

### 序列编号分配示例（重要参考！）

**正确的编号序列**：
```
0 - 文档标题
1 - 第一个章节标题
1.1 - 第一个章节下的第一个内容
2 - 第二个章节标题
2.1 - 第二个章节下的第一个内容
3 - 第三个章节标题
3.1 - 第三个章节下的第一个段落标题
3.1.1 - 第三个章节第一个段落下的第一个条目
3.1.2 - 第三个章节第一个段落下的第二个条目
3.2 - 第三个章节下的第二个段落标题
4 - 第四个章节标题
```

**错误的编号序列（避免！）**：
```
0 - 文档标题
1 - 第一个章节标题
2 - 第二个章节标题
2.1 - 第二个章节下的内容
4 - 第四个章节标题  ❌ 错误：跳过了3
```

请开始处理，输出包含基础控件的结构化数据。

<----------------------------(step1_output)---------------------------->


{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"content_segments": [
{
"segment_id": "seg_001",
"original_content": "# 慧芝湖花园3室2厅2卫价值评测报告",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "DOCUMENT",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "一级标题，作为文档主标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 1,
"importance_priority": "high"
},
"processing_notes": "文档主标题"
},
{
"segment_id": "seg_002",
"original_content": "## 报告基本信息",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_003",
"original_content": "- **数据来源**：上海市房地产交易平台\n- **评测时间**：2025年7月\n- **平均价格概览**：**97,600元/㎡**",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "包含关键信息项，使用BOARD样式突出显示",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "关键信息列表，包含价格数据"
},
{
"segment_id": "seg_004",
"original_content": "## 评测房源基本信息",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_005",
"original_content": "| 项目 | 详情 |\n|------|------|\n| 城市 | 上海市 |\n| 小区名称 | 慧芝湖花园 |\n| 户型 | 3室2厅2卫 |\n| 建筑面积 | 110㎡ |\n| 朝向 | 朝南 |\n| 预估单价 | 97,600元/㎡ |\n| 板块位置 | 凉城（挂牌板块：大宁板块） |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "BOARD",
"type_confidence": 1.0,
"style_confidence": 0.9,
"style_reasoning": "房源关键信息表格，使用BOARD样式突出显示",
"alternatives": [
{
"type": "TABLE",
"style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.5,
"reasoning": "普通表格样式备选方案"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "房源基本信息表格"
},
{
"segment_id": "seg_006",
"original_content": "**注**：本估值不包含装修价值",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "EMPHASIS",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "加粗的说明文本，使用EMPHASIS样式",
"alternatives": [
{
"type": "TEXT",
"style": "PLAIN",
"type_confidence": 0.9,
"style_confidence": 0.5,
"reasoning": "普通文本样式备选方案"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "medium"
},
"processing_notes": "附加说明文本"
},
{
"segment_id": "seg_007",
"original_content": "## 小区基本信息分析",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "章节标题，包含'分析'关键词"
},
{
"segment_id": "seg_008",
"original_content": "### 1. 小区户型分析",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "high"
},
"processing_notes": "段落标题，包含序号和分析关键词"
},
{
"segment_id": "seg_009",
"original_content": "#### 在售房源户型占比",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "ENTRY",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "四级标题，作为条目标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 4,
"importance_priority": "medium"
},
"processing_notes": "条目标题"
},
{
"segment_id": "seg_010",
"original_content": "| 户型 | 新增挂牌套数(套) | 挂牌均价(元/㎡) | 新增挂牌面积(㎡) |\n|------|------------------|-----------------|------------------|\n| 2室 | 2 | 100,000 | 196 |\n| 3室 | 3 | 106,985 | 398 |\n| 4室 | 2 | 103,667 | 300 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.8,
"style_reasoning": "多行数据表格，适合NORMAL样式",
"alternatives": [
{
"type": "CHART",
"style": "BAR",
"type_confidence": 0.7,
"style_confidence": 0.6,
"reasoning": "可转换为柱状图展示户型对比"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "户型数据表格"
},
{
"segment_id": "seg_011",
"original_content": "**户型评估**：小区在售房源以3室户型为主，占比**42.86%**，挂牌均价最高达106,985元/㎡；2室户型价格相对较低，为100,000元/㎡。",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "包含'评估'关键词和数据分析，使用BOARD样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "户型分析文本，包含关键数据和评估结论"
},
{
"segment_id": "seg_012",
"original_content": "#### 小区近12个月市场走势",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "ENTRY",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "四级标题，作为条目标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "条目标题，包含'走势'关键词"
},
{
"segment_id": "seg_013",
"original_content": "| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | - | 0.00 | 0 | 0 | 1 | 34 | 17,059 | -84.58 |\n| 2024年09月 | - | 0.00 | 0 | 0 | 0 | 0 | - | 0.00 |\n| 2024年10月 | 100,000 | 0.00 | 3 | 417 | 1 | 87 | 96,437 | 0.00 |\n| 2024年11月 | 106,473 | 6.47 | 5 | 482 | 3 | 357 | 91,120 | -5.51 |\n| 2024年12月 | 105,950 | -0.49 | 7 | 763 | 6 | 556 | 91,973 | 0.94 |\n| 2025年01月 | 102,416 | -3.34 | 2 | 178 | 1 | 88 | 96,591 | 5.02 |\n| 2025年02月 | 101,960 | -0.45 | 7 | 903 | 2 | 123 | 73,902 | -23.49 |\n| 2025年03月 | 109,001 | 6.91 | 10 | 1,201 | 2 | 296 | 93,176 | 26.08 |\n| 2025年04月 | 108,324 | -0.62 | 2 | 179 | 1 | 73 | 94,247 | 1.15 |\n| 2025年05月 | 107,222 | -1.02 | 4 | 468 | 3 | 238 | 85,882 | -8.88 |\n| 2025年06月 | 103,070 | -3.87 | 6 | 645 | 0 | 0 | - | 0.00 |\n| 2025年07月 | 105,689 | 0.00 | 4 | 559 | 0 | 0 | - | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.7,
"style_reasoning": "多行时间序列数据表格",
"alternatives": [
{
"type": "CHART",
"style": "LINE",
"type_confidence": 0.8,
"style_confidence": 0.8,
"reasoning": "适合转换为折线图展示趋势"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "时间序列数据表格"
},
{
"segment_id": "seg_014",
"original_content": "**趋势分析**：1. 挂牌均价呈现波动上升趋势，从2024年10月的100,000元/㎡升至2025年7月的105,689元/㎡ 2. 2025年3月达到挂牌均价峰值109,001元/㎡ 3. 成交活跃期集中在2024年11-12月，最高单月成交6套",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "包含'趋势分析'关键词和数据分析，使用BOARD样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "趋势分析文本，包含关键数据和结论"
},
{
"segment_id": "seg_015",
"original_content": "### 2. 板块市场对比分析",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "high"
},
"processing_notes": "段落标题，包含序号和'对比分析'关键词"
},
{
"segment_id": "seg_016",
"original_content": "#### 板块近12个月走势",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "ENTRY",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "四级标题，作为条目标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "条目标题，包含'走势'关键词"
},
{
"segment_id": "seg_017",
"original_content": "| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | 78,913 | -0.22 | 153 | 12,084 | 28 | 1,978 | 72,456 | -12.09 |\n| 2024年09月 | 82,594 | 4.66 | 173 | 14,040 | 31 | 2,305 | 76,633 | 5.76 |\n| 2024年10月 | 82,346 | -0.30 | 203 | 17,548 | 47 | 3,519 | 77,774 | 1.49 |\n| 2024年11月 | 82,061 | -0.35 | 191 | 16,101 | 63 | 4,917 | 79,483 | 2.20 |\n| 2024年12月 | 80,577 | -1.81 | 175 | 13,939 | 72 | 5,804 | 81,676 | 2.76 |\n| 2025年01月 | 77,387 | -3.96 | 90 | 7,322 | 34 | 2,889 | 79,855 | -2.23 |\n| 2025年02月 | 80,282 | 3.74 | 217 | 18,538 | 22 | 1,402 | 69,882 | -12.49 |\n| 2025年03月 | 81,956 | 2.09 | 226 | 19,118 | 82 | 6,573 | 74,976 | 7.29 |\n| 2025年04月 | 78,560 | -4.14 | 173 | 14,109 | 49 | 3,349 | 69,449 | -7.37 |\n| 2025年05月 | 79,206 | 0.82 | 190 | 15,946 | 50 | 3,688 | 71,457 | 2.89 |\n| 2025年06月 | 78,951 | -0.32 | 172 | 15,655 | 30 | 2,369 | 74,596 | 4.39 |\n| 2025年07月 | 76,071 | 0.00 | 108 | 10,025 | 4 | 356 | 60,253 | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.7,
"style_reasoning": "多行时间序列数据表格",
"alternatives": [
{
"type": "CHART",
"style": "LINE",
"type_confidence": 0.8,
"style_confidence": 0.8,
"reasoning": "适合转换为折线图展示趋势"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "时间序列数据表格"
},
{
"segment_id": "seg_018",
"original_content": "**板块对比分析**：1. 小区挂牌均价(105,689元/㎡)显著高于板块平均水平(76,071元/㎡)，溢价约39% 2. 小区成交均价波动较大，2025年2月出现异常低值73,902元/㎡ 3. 板块成交高峰出现在2024年12月，单月成交72套",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "包含'对比分析'关键词和数据分析，使用BOARD样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": true,
"structural_level": 4,
"importance_priority": "high"
},
"processing_notes": "对比分析文本，包含关键数据和结论"
},
{
"segment_id": "seg_019",
"original_content": "## 区域价值",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_020",
"original_content": "作为上海市静安区的核心居住板块，慧芝湖花园（三期）坐拥大宁国际商圈优质资源，45%高绿化率营造出都市绿洲般的居住环境。项目由嘉华(中国)投资有限公司开发，龙湖物业提供专业管理（物业费2.7元/月/㎡），完美融合国际化社区品质与便利生活体验。",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "PLAIN",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "描述性段落文本",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 2,
"importance_priority": "medium"
},
"processing_notes": "区域价值描述文本，包含数据信息"
},
{
"segment_id": "seg_021",
"original_content": "**区域核心价值体现于**：",
"content_type": "text",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "EMPHASIS",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "加粗的引导文本，使用EMPHASIS样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "强调文本，包含'价值体现'关键词"
},
{
"segment_id": "seg_022",
"original_content": "- **双轨交枢纽优势**：步行范围内覆盖1号线马戏城站与多条公交干线\n- **全龄教育资源矩阵**：1公里内覆盖幼儿园至小学优质教育机构\n- **商业配套集群**：百联莘荟购物中心等商业体形成5分钟生活圈\n- **生态宜居品质**：2.5低容积率与板楼设计保障居住舒适度",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "包含'优势'关键词的价值点列表，使用BOARD样式",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
},
"analysis_features": {
"has_analysis_keywords": true,
"has_numerical_data": true,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "核心价值点列表，包含数据和优势描述"
},
{
"segment_id": "seg_023",
"original_content": "## 交通网络",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "medium"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_024",
"original_content": "- **轨交动脉**：距1号线马戏城站约430米（步行6分钟），快速连接人民广场、徐家汇等核心商圈\n- **公交覆盖**：广中路平型关路站（305米）汇集107/547/767等8条公交线路，形成辐射全城的交通网络\n- **路网体系**：平型关路、广中路、共和新路构成三横三纵路网，15分钟车程直达内环高架",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "交通优势点列表，包含详细数据，使用BOARD样式",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 2,
"importance_priority": "high"
},
"processing_notes": "交通网络优势列表，包含详细距离数据"
},
{
"segment_id": "seg_025",
"original_content": "## 生活配套",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "medium"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_026",
"original_content": "### 医疗旗舰",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "段落标题"
},
{
"segment_id": "seg_027",
"original_content": "- 登特口腔（348米）：专业口腔医疗机构\n- 益丰大药房（166米）：24小时便民药房\n- 赞瞳眼科诊所（500米）：专科眼科服务",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "医疗设施列表，普通符号样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "医疗设施列表，包含距离数据"
},
{
"segment_id": "seg_028",
"original_content": "### 商业矩阵",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "段落标题"
},
{
"segment_id": "seg_029",
"original_content": "- 百联莘荟购物中心（500米）：**4.5星评级综合体**，内含盒马奥莱、Tims咖啡等品牌\n- 宝华现代城商业街（489米）：特色餐饮聚集地\n- 百果园（56米）：社区生鲜便利站",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "商业设施列表，普通符号样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "商业设施列表，包含距离数据和评级信息"
},
{
"segment_id": "seg_030",
"original_content": "### 休闲图鉴",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "段落标题"
},
{
"segment_id": "seg_031",
"original_content": "- 自然运动·普拉提（433米）：高端健身会所\n- 星巴克（199米）：社区咖啡社交空间\n- 和记小菜（308米）：4.6分评价的本帮菜餐厅",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "休闲设施列表，普通符号样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": true,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "休闲设施列表，包含距离数据和评分信息"
},
{
"segment_id": "seg_032",
"original_content": "## 教育资源",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "二级标题，作为章节标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 2,
"importance_priority": "medium"
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_033",
"original_content": "### 全龄教育链",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 1.0,
"style_confidence": 1.0,
"style_reasoning": "三级标题，作为段落标题",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
"has_numerical_data": false,
"structural_level": 3,
"importance_priority": "medium"
},
"processing_notes": "段落标题"
},
{
"segment_id": "seg_034",
"original_content": "- 大宁国际第二幼儿园（355米）：区级示范园\n- 上海市大宁国际小学（254米）：优质公办教育\n- 静安区大宁路小学（518米）：**历史悠久的重点小学**",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "教育设施列表，普通符号样式",
"alternatives": []
},
"analysis_features": {
"has_analysis_keywords": false,
